/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem 0; /* 从 2rem 减少到 1rem */
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
    font-size: 2rem; /* 从 2.5rem 减少到 2rem */
    margin-bottom: 0.3rem; /* 从 0.5rem 减少到 0.3rem */
    font-weight: 700;
}

.header-content h1 i {
    color: #3498db;
    margin-right: 0.5rem;
}

.subtitle {
    font-size: 1rem; /* 从 1.1rem 减少到 1rem */
    margin-bottom: 1rem; /* 从 1.5rem 减少到 1rem */
    opacity: 0.9;
}

.team-info {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.team-member {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.3rem 0.8rem; /* 从 0.5rem 1rem 减少到 0.3rem 0.8rem */
    border-radius: 20px;
    font-size: 0.8rem; /* 从 0.9rem 减少到 0.8rem */
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.team-member i {
    margin-right: 0.3rem;
    color: #3498db;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    flex: 1;
    gap: 2rem;
    padding: 1.5rem; /* 从 2rem 减少到 1.5rem */
}

/* 侧边栏样式 */
.sidebar {
    width: 300px;
    min-width: 300px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar.collapsed {
    width: 60px;
    min-width: 60px;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1rem;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-header h3 {
    opacity: 0;
    pointer-events: none;
}

.toggle-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.toggle-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.new-chat-btn {
    background: #007bff;
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.new-chat-btn:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.sidebar.collapsed .new-chat-btn {
    opacity: 0;
    pointer-events: none;
}

.sidebar-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-content {
    opacity: 0;
    pointer-events: none;
}

.search-box {
    position: relative;
    margin: 1rem;
    margin-bottom: 0.5rem;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 0.9rem;
    outline: none;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 0.9rem;
}

.history-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 1rem 1rem;
}

.history-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    position: relative;
}

.history-item:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
}

.history-item.active {
    background: #e3f2fd;
    border-color: #007bff;
}

.history-item-title {
    font-weight: 500;
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.history-item-preview {
    font-size: 0.8rem;
    color: #6c757d;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.history-item-time {
    font-size: 0.75rem;
    color: #adb5bd;
    margin-top: 0.25rem;
}

.history-item-actions {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.history-item:hover .history-item-actions {
    opacity: 1;
}

.history-action-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.25rem;
    border-radius: 4px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.history-action-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.no-history {
    text-align: center;
    padding: 2rem 1rem;
    color: #6c757d;
}

.no-history i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-history p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

/* 旧的控制面板样式已移除 */

/* 聊天区域 */
.chat-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chat-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chat-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.session-info {
    font-size: 0.9rem;
    color: #6c757d;
    padding: 0.25rem 0.75rem;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(0, 123, 255, 0.2);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.connection-status.connected {
    color: #2ecc71;
}

.connection-status.disconnected {
    color: #e74c3c;
}

.connection-status i {
    font-size: 0.7rem;
}

/* 聊天容器 */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 280px); /* 设置容器高度，为头部、页脚等预留空间 */
    position: relative; /* 为新消息指示器提供定位基准 */
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    min-height: 0; /* 确保flex子元素可以收缩 */
    max-height: calc(100vh - 320px); /* 设置最大高度，约束对话区域 */
    background: #f8f9fa;
    /* 美化滚动条 */
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 欢迎消息 */
.welcome-message {
    text-align: center;
    padding: 3rem 2rem;
}

.welcome-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    border: 2px dashed #3498db;
}

.welcome-content i {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.welcome-content h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.welcome-content p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* 消息样式 */
.message {
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3498db, #2980b9);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.message-name {
    font-weight: 600;
    color: #2c3e50;
}

.message-time {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-left: auto;
}

.message-content {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3498db;
    line-height: 1.6;
}

.message.user .message-content {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-left: 4px solid #2980b9;
    margin-left: 2rem;
}

.message.system .message-content {
    background: #f39c12;
    color: white;
    border-left: 4px solid #e67e22;
}

/* 输入区域 */
.chat-input-container {
    padding: 1.5rem;
    background: white;
    border-top: 1px solid #ecf0f1;
}

.input-wrapper {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

#messageInput {
    flex: 1;
    padding: 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

#messageInput:focus {
    border-color: #3498db;
}

#messageInput:disabled {
    background: #f8f9fa;
    color: #6c757d;
}

.send-btn {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
}

.send-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

.input-hint {
    font-size: 0.8rem;
    color: #7f8c8d;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

/* 页脚 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 1rem; /* 从 1.5rem 减少到 1rem */
    text-align: center;
}

.footer-content p {
    margin-bottom: 0.5rem;
}

.footer-links {
    font-size: 0.9rem;
    opacity: 0.8;
}

.footer-links span {
    margin: 0 0.5rem;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 通知样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.notification {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 0.5rem;
    border-left: 4px solid #3498db;
    animation: slideInRight 0.3s ease;
}

.notification.error {
    border-left-color: #e74c3c;
}

.notification.success {
    border-left-color: #2ecc71;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 工具调用样式 */
.tool-call {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.8rem;
    margin: 0.5rem 0;
    font-size: 0.9rem;
    border-left: 4px solid #17a2b8;
}

.tool-call .tool-icon {
    color: #17a2b8;
    margin-right: 0.5rem;
}

.tool-call .tool-name {
    font-weight: 600;
    color: #495057;
}

.tool-call .tool-params {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    margin-top: 0.3rem;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6c757d;
    word-break: break-all;
}

.tool-result {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 0.6rem;
    margin: 0.5rem 0;
    font-size: 0.9rem;
    border-left: 4px solid #28a745;
}

.tool-result .tool-icon {
    color: #28a745;
    margin-right: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        padding: 1rem;
    }

    .sidebar {
        width: 100%;
        order: 2;
    }

    .chat-section {
        order: 1;
        min-height: calc(60vh); /* 在移动端使用视口高度的60% */
    }

    .chat-container {
        height: calc(100vh - 320px); /* 移动端调整高度计算 */
    }

    .chat-messages {
        max-height: calc(100vh - 360px); /* 移动端调整对话区域最大高度 */
    }

    .team-info {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem; /* 减少间距 */
    }

    .header-content h1 {
        font-size: 1.8rem; /* 移动端进一步减小标题 */
    }

    .header {
        padding: 0.8rem 0; /* 移动端减少头部内边距 */
    }
}

/* Call @xxx 样式 */
.call-mention {
    color: #007bff;
    font-weight: bold;
    background: rgba(0, 123, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid rgba(0, 123, 255, 0.2);
    display: inline-block;
    margin: 0 2px;
}

.call-mention:hover {
    background: rgba(0, 123, 255, 0.2);
    cursor: pointer;
}

/* 连续消息样式 */
.message-group {
    margin-bottom: 1rem;
}

.message-continuation {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 工具调用折叠样式 */
.tool-collapsible {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin: 8px 0;
    overflow: hidden;
}

.tool-header {
    background: #e9ecef;
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    transition: background-color 0.2s;
}

.tool-header:hover {
    background: #dee2e6;
}

.tool-header .tool-icon {
    margin-right: 8px;
}

.tool-toggle {
    font-size: 12px;
    color: #6c757d;
    transition: transform 0.2s;
}

.tool-toggle.expanded {
    transform: rotate(180deg);
}

.tool-content {
    padding: 12px;
    background: #ffffff;
    border-top: 1px solid #e9ecef;
    display: none;
    font-size: 0.9em;
    max-height: 300px;
    overflow-y: auto;
}

.tool-content.expanded {
    display: block;
}

/* 工具属性样式 */
.tool-property {
    margin-bottom: 12px;
}

.tool-property:last-child {
    margin-bottom: 0;
}

.property-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 6px;
    font-size: 0.9em;
}

.property-value {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
}

.property-value pre {
    margin: 0;
    padding: 12px;
    background: transparent;
    border: none;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.85em;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
    overflow-x: auto;
}

.property-value code {
    background: transparent;
    padding: 0;
    border: none;
    font-family: inherit;
    color: #212529;
}

/* 工具内容文本样式 */
.tool-content-text {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.5;
    color: #495057;
}

.tool-content-markdown {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.5;
}

/* 工具调用特定样式 */
.tool-call-collapsible .tool-header {
    background: #e3f2fd;
    color: #1976d2;
}

.tool-call-collapsible .tool-header:hover {
    background: #bbdefb;
}

.tool-call-collapsible .property-value {
    background: #f3f8ff;
    border-color: #d1e7ff;
}

/* 工具结果特定样式 */
.tool-result-collapsible .tool-header {
    background: #e8f5e8;
    color: #388e3c;
}

.tool-result-collapsible .tool-header:hover {
    background: #c8e6c9;
}

.tool-result-collapsible .property-value {
    background: #f8fff8;
    border-color: #d4edda;
}

/* 新闻结果样式 */
.news-results {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-height: 250px;
    overflow-y: auto;
}

.news-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 16px;
    transition: box-shadow 0.2s ease;
}

.news-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.news-item:last-child {
    margin-bottom: 0;
}

.news-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;
}

.news-title {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
    line-height: 1.4;
    flex: 1;
    margin-right: 12px;
}

.news-title a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.2s ease;
}

.news-title a:hover {
    color: #3498db;
    text-decoration: underline;
}

.news-index {
    background: #6c757d;
    color: white;
    font-size: 0.8em;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.news-content {
    color: #495057;
    line-height: 1.6;
    margin-bottom: 12px;
    font-size: 0.95em;
}

.news-link {
    text-align: right;
}

.news-link a {
    color: #6c757d;
    text-decoration: none;
    font-size: 0.9em;
    transition: color 0.2s ease;
}

.news-link a:hover {
    color: #3498db;
}

.news-link i {
    margin-right: 4px;
}

.news-empty {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

.news-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
}

/* 滚动条样式美化 */
.chat-messages::-webkit-scrollbar,
.tool-content::-webkit-scrollbar,
.property-value::-webkit-scrollbar,
.news-results::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track,
.tool-content::-webkit-scrollbar-track,
.property-value::-webkit-scrollbar-track,
.news-results::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb,
.tool-content::-webkit-scrollbar-thumb,
.property-value::-webkit-scrollbar-thumb,
.news-results::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.tool-content::-webkit-scrollbar-thumb:hover,
.property-value::-webkit-scrollbar-thumb:hover,
.news-results::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 聊天消息区域滚动条特别样式 */
.chat-messages::-webkit-scrollbar {
    width: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 5px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
}

/* 快速示例标签 */
.quick-examples {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
    justify-content: center;
}

.example-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(25, 118, 210, 0.2);
}

.example-tag:hover {
    background: #1976d2;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

/* 新消息指示器 */
.new-message-indicator {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    cursor: pointer;
    display: none;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
    font-size: 0.9rem;
    font-weight: 500;
    animation: pulse 2s infinite;
}

.new-message-indicator:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
}

.new-message-indicator i {
    font-size: 0.9rem;
    animation: bounce 1s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}
