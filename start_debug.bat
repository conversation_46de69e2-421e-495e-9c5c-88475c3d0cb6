@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
title 股票咨询代理系统 (测试模式)

echo.
echo ========================================
echo    股票咨询代理系统 - 测试模式
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在检查依赖包...
python -c "import flask, flask_socketio" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少必要依赖，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查通过
echo.

echo 正在检查测试消息文件...
if not exist "messages\session_9c4ab546-48d6-43a1-8a3b-0fef81ed87d6.jsonl" (
    echo ❌ 测试消息文件不存在
    echo 请确保 messages\session_9c4ab546-48d6-43a1-8a3b-0fef81ed87d6.jsonl 文件存在
    pause
    exit /b 1
)

echo ✅ 测试消息文件检查通过
echo.

echo 🧪 启动测试模式...
echo 📝 测试模式说明：
echo    - 系统将加载预设的测试消息
echo    - 无需启动代理进程
echo    - 可以测试前端显示效果
echo    - 要切换到正常模式，请修改 app_file.py 中的 DEBUG_MODE = False
echo.

python app_file.py

pause
