#!/usr/bin/env python3
"""
切换 app_file.py 的测试模式
"""

import os
import sys
import re

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.system('chcp 65001 >nul 2>&1')
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

def toggle_debug_mode():
    """切换测试模式"""
    
    app_file = "app_file.py"
    
    if not os.path.exists(app_file):
        print(f"❌ 文件不存在: {app_file}")
        return
    
    # 读取文件内容
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找当前的DEBUG_MODE设置
    debug_pattern = r'DEBUG_MODE\s*=\s*(True|False)'
    match = re.search(debug_pattern, content)
    
    if not match:
        print("❌ 未找到 DEBUG_MODE 设置")
        return
    
    current_mode = match.group(1)
    new_mode = "False" if current_mode == "True" else "True"
    
    # 替换模式
    new_content = re.sub(debug_pattern, f'DEBUG_MODE = {new_mode}', content)
    
    # 写回文件
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    mode_name = "测试模式" if new_mode == "True" else "正常模式"
    print(f"✅ 已切换到 {mode_name} (DEBUG_MODE = {new_mode})")
    
    if new_mode == "True":
        print("🧪 测试模式说明：")
        print("   - 系统将加载预设的测试消息文件")
        print("   - 无需启动代理进程")
        print("   - 可以测试前端显示效果")
        print("   - 运行: python app_file.py 或 start_debug.bat")
    else:
        print("🚀 正常模式说明：")
        print("   - 系统将启动完整的代理进程")
        print("   - 需要配置好 agent_v3.py 和相关依赖")
        print("   - 运行: python app_file.py")

def show_current_mode():
    """显示当前模式"""
    
    app_file = "app_file.py"
    
    if not os.path.exists(app_file):
        print(f"❌ 文件不存在: {app_file}")
        return
    
    # 读取文件内容
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找当前的DEBUG_MODE设置
    debug_pattern = r'DEBUG_MODE\s*=\s*(True|False)'
    match = re.search(debug_pattern, content)
    
    if not match:
        print("❌ 未找到 DEBUG_MODE 设置")
        return
    
    current_mode = match.group(1)
    mode_name = "测试模式" if current_mode == "True" else "正常模式"
    
    print(f"📊 当前模式: {mode_name} (DEBUG_MODE = {current_mode})")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "status":
        show_current_mode()
    else:
        print("🔄 切换 app_file.py 的运行模式")
        print("=" * 40)
        show_current_mode()
        print()
        toggle_debug_mode()
        print()
        print("💡 提示：")
        print("   - 查看当前模式: python toggle_debug_mode.py status")
        print("   - 切换模式: python toggle_debug_mode.py")
