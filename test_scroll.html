<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动测试页面</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .test-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }
        .add-message-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 0.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h2>聊天滚动测试</h2>
            <button class="add-message-btn" onclick="addTestMessage()">添加消息</button>
            <button class="add-message-btn" onclick="addLongMessage()">添加长消息</button>
            <button class="add-message-btn" onclick="clearMessages()">清空消息</button>
        </div>
        
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <div class="message-header">
                        <div class="message-avatar">🤖</div>
                        <span class="message-name">测试助手</span>
                        <span class="message-time">12:00:00</span>
                    </div>
                    <div class="message-content">
                        欢迎来到滚动测试页面！这里用来测试聊天区域的固定高度和滚动功能。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 1;
        
        function addTestMessage() {
            messageCount++;
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant';
            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="message-avatar">🤖</div>
                    <span class="message-name">测试助手</span>
                    <span class="message-time">${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="message-content">
                    这是第 ${messageCount} 条测试消息。内容相对简短，用于测试基本的滚动功能。
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function addLongMessage() {
            messageCount++;
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant';
            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="message-avatar">📝</div>
                    <span class="message-name">长文本助手</span>
                    <span class="message-time">${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="message-content">
                    这是第 ${messageCount} 条长消息测试。这条消息包含了大量的文本内容，用于测试当消息内容很长时，滚动条是否能正常工作。
                    <br><br>
                    在实际使用中，股票分析代理可能会生成很长的分析报告，包含：
                    <br>• 市场趋势分析
                    <br>• 技术指标解读
                    <br>• 基本面分析
                    <br>• 风险评估
                    <br>• 投资建议
                    <br><br>
                    这些内容可能会占用很多屏幕空间，因此需要确保聊天区域有合适的高度限制和滚动功能。
                    滚动条应该样式美观，并且在用户向上查看历史消息时，新消息到达时不应该强制跳转到底部，
                    而是显示一个"有新消息"的提示按钮。
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function clearMessages() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="message assistant">
                    <div class="message-header">
                        <div class="message-avatar">🤖</div>
                        <span class="message-name">测试助手</span>
                        <span class="message-time">${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="message-content">
                        消息已清空，重新开始测试。
                    </div>
                </div>
            `;
            messageCount = 1;
        }
    </script>
</body>
</html> 