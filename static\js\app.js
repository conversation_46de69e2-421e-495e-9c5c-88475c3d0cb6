// 全局变量
let socket;
let isConnected = false;
let md; // markdown-it 实例
let currentSessionId = null;
let chatHistory = [];

// 测试模式配置 - 与后端保持一致
const DEBUG_MODE = false;  // 设置为 true 启用测试模式
const DEBUG_SESSION_ID = "07200dd0-cec7-4b06-826b-4384bcfe6f27";  // 测试用的会话ID

// DOM元素
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const chatMessages = document.getElementById('chatMessages');
const connectionStatus = document.getElementById('connectionStatus');
const loadingOverlay = document.getElementById('loadingOverlay');
const notificationContainer = document.getElementById('notificationContainer');

// 新的DOM元素
const toggleSidebar = document.getElementById('toggleSidebar');
const newChatBtn = document.getElementById('newChatBtn');
const searchHistory = document.getElementById('searchHistory');
const historyList = document.getElementById('historyList');
const sessionInfo = document.getElementById('sessionInfo');
const sidebar = document.querySelector('.sidebar');

// 历史对话存储键
const STORAGE_KEY = 'stock_agent_chat_history';

// 初始化 markdown-it
function initializeMarkdown() {
    try {
        // 创建 markdown-it 实例，启用常用插件
        md = window.markdownit({
            html: true,        // 允许HTML标签
            breaks: true,      // 将换行符转换为<br>
            linkify: true,     // 自动识别链接
            typographer: true  // 启用一些语言中性的替换和引号美化
        });

        console.log('Markdown-it 初始化成功');
        return true;
    } catch (error) {
        console.error('Markdown-it 初始化失败:', error);
        return false;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    // 初始化 markdown-it
    if (!initializeMarkdown()) {
        console.warn('Markdown-it 初始化失败，将使用备用方案');
    }

    initializeWebSocket();
    bindEvents();
    setupExampleTags();
    setupScrollListener();
    loadChatHistory();
    startNewSession();
});

// 初始化WebSocket连接
function initializeWebSocket() {
    socket = io();
    
    socket.on('connect', function() {
        isConnected = true;
        updateConnectionStatus('connected', '已连接');
        showNotification('连接成功', 'success');
    });
    
    socket.on('disconnect', function() {
        isConnected = false;
        updateConnectionStatus('disconnected', '连接断开');
        showNotification('连接断开', 'error');
    });
    
    socket.on('status', function(data) {
        showNotification(data.message, 'info');
    });
    
    socket.on('agent_message', function(data) {
        addMessage(data);
    });

    // 监听消息更新
    socket.on('update_message', function(data) {
        updateLastMessage(data);
    });

    socket.on('error', function(data) {
        showNotification(data.message, 'error');
    });

    // 处理会话创建事件
    socket.on('session_created', function(data) {
        const serverSessionId = data.session_id;
        console.log('收到服务器会话ID:', serverSessionId);
        
        // 更新当前会话ID为服务器生成的ID
        currentSessionId = serverSessionId;
        
        // 更新会话信息显示
        updateSessionInfo(`会话: ${serverSessionId.substring(0, 8)}...`);
        
        // 如果有待创建的会话标题，现在创建历史记录
        if (window.pendingSessionTitle) {
            updateSessionTitle(window.pendingSessionTitle);
            window.pendingSessionTitle = null; // 清除暂存的标题
        }
    });
}

// 绑定事件
function bindEvents() {
    // 发送按钮
    sendBtn.addEventListener('click', sendMessage);
    
    // 输入框回车发送
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // 侧边栏折叠切换
    toggleSidebar.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
    });
    
    // 新建对话
    newChatBtn.addEventListener('click', function() {
        startNewSession();
    });
    
    // 搜索历史对话
    searchHistory.addEventListener('input', function() {
        filterHistory(this.value);
    });
}

// 设置示例标签点击事件
function setupExampleTags() {
    const exampleTags = document.querySelectorAll('.example-tag');
    exampleTags.forEach(tag => {
        tag.addEventListener('click', function() {
            const text = this.getAttribute('data-text');
            messageInput.value = text;
            sendMessage();
        });
    });
}

// 开始新会话
function startNewSession() {
    // 在非测试模式下，不提前生成会话ID，等待后端分配
    if (DEBUG_MODE) {
        currentSessionId = generateSessionId();
    } else {
        currentSessionId = null;
    }
    clearChatMessages();
    
    if (DEBUG_MODE) {
        // 测试模式：检查是否已有测试会话的历史记录
        const existingSession = chatHistory.find(s => s.id === DEBUG_SESSION_ID);
        if (!existingSession) {
            // 创建测试会话的历史记录
            chatHistory.push({
                id: DEBUG_SESSION_ID,
                title: '🧪 测试会话',
                timestamp: new Date().toISOString()
            });
            saveChatHistory();
            renderHistoryList();
            console.log('🧪 测试模式：创建了测试会话的历史记录');
        }
        updateSessionInfo('🧪 测试会话');
        
        // 自动加载测试会话的消息
        setTimeout(() => {
            loadHistorySession(DEBUG_SESSION_ID);
        }, 100);
    } else {
        updateSessionInfo('新会话');
        
        // 显示欢迎消息
        const welcomeDiv = document.querySelector('.welcome-message');
        if (welcomeDiv) {
            welcomeDiv.style.display = 'block';
        }
    }
    
    // 清除活跃的历史项目
    document.querySelectorAll('.history-item.active').forEach(item => {
        item.classList.remove('active');
    });
}

// 生成会话ID
function generateSessionId() {
    if (DEBUG_MODE) {
        console.log('🧪 测试模式：使用固定的测试会话ID');
        return DEBUG_SESSION_ID;
    }
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 更新会话信息
function updateSessionInfo(info) {
    sessionInfo.textContent = info;
}

// 清空聊天消息
function clearChatMessages(showWelcome = true) {
    if (showWelcome) {
        chatMessages.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-content">
                    <i class="fas fa-hand-wave"></i>
                    <h3>欢迎使用股票咨询代理系统！</h3>
                    <p>您可以直接输入您想了解的股票题材或相关问题，我们的专家团队将为您提供专业分析。</p>
                    <div class="quick-examples">
                        <span class="example-tag" data-text="请分析人工智能题材">人工智能题材</span>
                        <span class="example-tag" data-text="分析新能源汽车板块">新能源汽车</span>
                        <span class="example-tag" data-text="半导体行业最新动态">半导体行业</span>
                        <span class="example-tag" data-text="医药生物题材机会">医药生物</span>
                    </div>
                </div>
            </div>
        `;
        setupExampleTags();
    } else {
        chatMessages.innerHTML = '';
    }
}

// 保存聊天历史
function saveChatHistory() {
    try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(chatHistory));
    } catch (error) {
        console.error('保存聊天历史失败:', error);
    }
}

// 加载聊天历史
function loadChatHistory() {
    try {
        const saved = localStorage.getItem(STORAGE_KEY);
        if (saved) {
            chatHistory = JSON.parse(saved);
            // 清理无效的历史记录（id为null或undefined的记录）
            const originalLength = chatHistory.length;
            chatHistory = chatHistory.filter(session => session.id && session.id !== 'null');
            if (chatHistory.length !== originalLength) {
                console.log(`清理了 ${originalLength - chatHistory.length} 条无效历史记录`);
                saveChatHistory(); // 保存清理后的历史记录
            }
        }
        renderHistoryList();
    } catch (error) {
        console.error('加载聊天历史失败:', error);
        chatHistory = [];
    }
}

// 渲染历史对话列表
function renderHistoryList(filteredHistory = null) {
    const history = filteredHistory || chatHistory;

    if (history.length === 0) {
        historyList.innerHTML = `
            <div class="no-history">
                <i class="fas fa-comment-slash"></i>
                <p>暂无历史对话</p>
                <p>开始新的对话吧！</p>
            </div>
        `;
        return;
    }

    // 反转数组，让最新的对话显示在最上面
    const sortedHistory = [...history].reverse();

    const historyHTML = sortedHistory.map(session => {
        const timeStr = new Date(session.timestamp).toLocaleString();
        
        return `
            <div class="history-item" data-session-id="${session.id}">
                <div class="history-item-title">${session.title}</div>
                <div class="history-item-preview">点击加载对话内容</div>
                <div class="history-item-time">${timeStr}</div>
                <div class="history-item-actions">
                    <button class="history-action-btn" onclick="deleteHistoryItem('${session.id}', event)" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    historyList.innerHTML = historyHTML;
    
    // 绑定点击事件
    document.querySelectorAll('.history-item').forEach(item => {
        item.addEventListener('click', function() {
            const sessionId = this.getAttribute('data-session-id');
            loadHistorySession(sessionId);
        });
    });
}

// 过滤历史对话
function filterHistory(searchTerm) {
    if (!searchTerm.trim()) {
        renderHistoryList();
        return;
    }
    
    const filtered = chatHistory.filter(session => 
        session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (session.messages && session.messages.some(msg => 
            msg.content.toLowerCase().includes(searchTerm.toLowerCase())
        ))
    );
    
    renderHistoryList(filtered);
}

// 加载历史会话
async function loadHistorySession(sessionId) {
    const session = chatHistory.find(s => s.id === sessionId);
    if (!session) return;
    
    currentSessionId = sessionId;
    clearChatMessages(false); // 加载历史时不显示欢迎信息
    updateSessionInfo(session.title);
    
    // 显示加载状态
    showLoadingOverlay(true);
    
    try {
        // 从服务端获取会话消息
        const response = await fetch(`/api/session/${sessionId}/messages`);
        if (response.ok) {
            const messages = await response.json();
            
            // 恢复消息
            messages.forEach(message => {
                addMessage(message, false); // false 表示不保存到历史
            });
        } else {
            console.error('加载历史会话失败:', response.status);
            showNotification('加载历史会话失败', 'error');
        }
    } catch (error) {
        console.error('加载历史会话出错:', error);
        showNotification('加载历史会话出错', 'error');
    } finally {
        showLoadingOverlay(false);
    }
    
    // 更新活跃状态
    document.querySelectorAll('.history-item.active').forEach(item => {
        item.classList.remove('active');
    });
    const targetItem = document.querySelector(`[data-session-id="${sessionId}"]`);
    if (targetItem) {
        targetItem.classList.add('active');
    }
}

// 删除历史项目
function deleteHistoryItem(sessionId, event) {
    if (event) {
        event.stopPropagation(); // 阻止冒泡
    }

    if (confirm('确定要从历史记录中删除这个对话吗？')) {
        // 只有当sessionId不为null时才调用后端API
        if (sessionId && sessionId !== 'null') {
            fetch(`/api/session/${sessionId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    console.log('会话删除成功:', data.message);
                } else {
                    console.warn('删除会话时出现问题:', data.error);
                }
            })
            .catch(error => {
                console.error('删除会话请求失败:', error);
            });
        } else {
            console.log('跳过删除无效会话ID的后端请求:', sessionId);
        }

        // 从前端历史记录中删除
        chatHistory = chatHistory.filter(s => s.id !== sessionId);
        saveChatHistory();
        renderHistoryList();

        // 如果删除的是当前会话，开始新会话
        if (currentSessionId === sessionId) {
            startNewSession();
        }
    }
}

// 发送消息
function sendMessage() {
    const message = messageInput.value.trim();
    if (!message) {
        return;
    }
    
    // 在非测试模式下，会话ID由后端生成
    
    // 隐藏欢迎消息
    const welcomeDiv = document.querySelector('.welcome-message');
    if (welcomeDiv) {
        welcomeDiv.style.display = 'none';
    }
    
    // 添加用户消息到界面
    addMessage({
        name: 'User',
        content: message,
        timestamp: new Date().toLocaleTimeString(),
        role: 'user',
        icon: '👤'
    });
    
    // 发送到服务器
    socket.emit('send_message', { message: message });
    
    // 清空输入框
    messageInput.value = '';
    messageInput.focus();
    
    // 暂存消息内容，等待后端分配会话ID后再创建历史记录
    window.pendingSessionTitle = message.length > 30 ? message.substring(0, 30) + '...' : message;
}

// 更新会话标题
function updateSessionTitle(message) {
    const title = message.length > 30 ? message.substring(0, 30) + '...' : message;
    updateSessionInfo(title);
    
    // 如果有会话ID，更新或创建历史记录
    if (currentSessionId) {
        const currentSession = chatHistory.find(s => s.id === currentSessionId);
        if (currentSession) {
            // 更新现有会话的标题
            currentSession.title = title;
        } else {
            // 创建新的历史记录
            chatHistory.push({
                id: currentSessionId,
                title: title,
                timestamp: new Date().toISOString()
            });
        }
        saveChatHistory();
        renderHistoryList();
    }
}

// 更新最后一条消息的内容 - 简化版本
function updateLastMessage(data) {
    console.log('🔄 收到消息更新请求:', data);
    console.log('🔍 完整消息内容:', data.content);

    const messages = chatMessages.querySelectorAll('.message');
    if (messages.length === 0) {
        console.log('❌ 没有消息可更新');
        return;
    }

    const lastMessage = messages[messages.length - 1];
    const lastMessageContent = lastMessage.querySelector('.message-content');

    if (!lastMessageContent) {
        console.log('❌ 最后一条消息没有内容区域');
        return;
    }

    // 检查是否有raw_content，如果有tool_result，优先处理
    if (data.raw_content && Array.isArray(data.raw_content)) {
        const hasToolResult = data.raw_content.some(item =>
            item && typeof item === 'object' && item.type === 'tool_result'
        );

        if (hasToolResult) {
            console.log('🔍 检测到raw_content中的tool_result，尝试合并');
            if (mergeToolResultToLastMessage(data)) {
                console.log('✅ 通过raw_content合并成功');
                return;
            }
        }
    }

    // 如果没有raw_content，但是内容包含search_stock_news工具结果，尝试特殊处理
    if (data.content && data.content.includes('search_stock_news') && data.content.includes('✅ **工具执行完成**')) {
        console.log('🔍 检测到search_stock_news工具结果，但没有raw_content');
        // 这种情况下，我们需要确保工具结果能够正确显示为新闻格式
        // 通过修改processToolCalls函数来处理
    }

    // 直接更新内容，不进行额外处理（内容已经在addMessage中处理过了）
    if (data.content) {
        console.log('📝 更新内容长度:', data.content.length);

        // 检查内容是否已经包含HTML（如工具折叠组件）
        const hasHTML = data.content.includes('<div class="tool-collapsible"') ||
                       data.content.includes('<span class="call-mention"');
        const hasToolCalls = data.content.includes('🔧 **工具调用**:') ||
                            data.content.includes('✅ **工具执行完成**:');

        let processedContent = data.content;

        if (hasHTML) {
            // 如果已经包含HTML组件，直接使用
            console.log('✅ 检测到HTML组件，直接使用内容');
        } else if (hasToolCalls) {
            // 如果包含工具调用格式，使用与addMessage相同的处理逻辑
            console.log('✅ 检测到工具调用格式，进行完整处理');

            // 先处理 Call mentions
            processedContent = processCallMentions(data.content);

            // 然后处理工具调用 - 这里需要处理可能包含多个工具调用的内容
            processedContent = processToolCalls(processedContent);

            // 对于包含HTML组件的内容，不要全局替换换行符
            // processedContent = processedContent.replace(/\n/g, '<br>');
        } else {
            // 只对纯文本内容进行Markdown处理
            if (md) {
                try {
                    processedContent = md.render(data.content);
                    console.log('✅ Markdown处理成功');
                } catch (error) {
                    console.error('Markdown处理失败，使用原始内容:', error);
                    processedContent = data.content.replace(/\n/g, '<br>');
                }
            } else {
                processedContent = data.content.replace(/\n/g, '<br>');
            }
        }

        // 检查是否需要累积内容
        const hasExistingContent = lastMessageContent.innerHTML.trim() !== '';
        const hasToolCallInOriginal = data.content.includes('🔧 **工具调用**:') || data.content.includes('✅ **工具执行完成**:');
        const shouldAppend = hasToolCallInOriginal && hasExistingContent;

        console.log('🔍 累积判断:', {
            hasToolCallInOriginal,
            hasExistingContent,
            shouldAppend,
            existingContentLength: lastMessageContent.innerHTML.length
        });

        if (shouldAppend) {
            // 如果是工具调用更新且已有内容，则只追加新的工具结果部分
            console.log('✅ 追加工具调用内容');
            console.log('🔍 原始新内容预览:', data.content.substring(0, 200) + '...');

            // 只提取和处理工具结果部分
            const toolResultsOnly = extractToolResults(data.content);
            if (toolResultsOnly) {
                const processedResults = processToolCalls(toolResultsOnly);
                lastMessageContent.innerHTML += processedResults;
            }
        } else {
            // 否则替换内容
            console.log('✅ 替换消息内容');
            console.log('🔍 处理后内容预览:', processedContent.substring(0, 200) + '...');
            lastMessageContent.innerHTML = processedContent;
        }
        console.log('✅ 消息内容已更新');

        // 使用 requestAnimationFrame 确保DOM更新后再滚动
        requestAnimationFrame(() => {
            scrollToBottom();
        });
    } else {
        console.log('⚠️ 没有内容可更新');
    }
}

// 从raw_content中提取内容
function extractContentFromRawContent(rawContent) {
    let extractedContent = '';

    if (!Array.isArray(rawContent)) {
        return extractedContent;
    }

    for (const item of rawContent) {
        if (!item || typeof item !== 'object') continue;

        if (item.type === 'text') {
            extractedContent += item.text || '';
        } else if (item.type === 'tool_use') {
            const toolName = item.name || '未知工具';
            const toolInput = item.input || {};
            extractedContent += `🔧 **工具调用**: ${toolName}\n📋 **参数**: \`${JSON.stringify(toolInput, null, 2)}\`\n\n`;
        } else if (item.type === 'tool_result') {
            const toolName = item.name || '工具';
            const output = item.output || [];

            if (output.length > 0 && output[0].text) {
                const outputText = output[0].text;
                extractedContent += `✅ **工具执行完成**: ${toolName}\n📊 **返回结果**: ${outputText}\n\n`;
            } else {
                extractedContent += `✅ **工具执行完成**: ${toolName}\n📊 **返回结果**: 执行完成，无返回数据\n\n`;
            }
        }
    }

    return extractedContent;
}

// 尝试将tool_result合并到上一个消息
function mergeToolResultToLastMessage(data) {
    console.log('🔍 开始尝试合并tool_result');

    const messages = chatMessages.querySelectorAll('.message');
    console.log(`当前消息数量: ${messages.length}`);

    if (messages.length === 0) {
        console.log('❌ 没有上一个消息，无法合并');
        return false; // 没有上一个消息
    }

    const lastMessage = messages[messages.length - 1];
    const lastMessageContent = lastMessage.querySelector('.message-content');

    if (!lastMessageContent) {
        console.log('❌ 上一个消息没有内容区域');
        return false;
    }

    // 检查上一个消息是否包含工具调用（通过查找🔧图标或tool_use关键词）
    const lastMessageHTML = lastMessageContent.innerHTML;
    const hasToolUse = lastMessageHTML.includes('🔧') || lastMessageHTML.includes('正在调用工具');

    console.log('上一个消息HTML片段:', lastMessageHTML.substring(0, 100) + '...');
    console.log('上一个消息是否包含工具调用:', hasToolUse);

    if (!hasToolUse) {
        console.log('❌ 上一个消息不包含工具调用，不进行合并');
        return false;
    }

    console.log('✅ 上一个消息包含工具调用，可以合并tool_result');

    // 处理tool_result内容
    let toolResultComponents = '';
    if (data.raw_content && Array.isArray(data.raw_content)) {
        for (const item of data.raw_content) {
            if (item && item.type === 'tool_result') {
                const toolName = item.name || '工具';
                const output = item.output || [];

                if (output.length > 0 && output[0].text) {
                    const outputText = output[0].text;

                    // 检查是否是错误信息
                    if (outputText.includes('validation error') || outputText.includes('未找到')) {
                        const title = `工具执行失败: ${toolName}`;
                        let content = '';

                        // 为不同的错误类型提供更友好的提示
                        if (outputText.includes('未找到相关新闻')) {
                            content = `暂时没有找到相关的新闻信息，可能是因为：\n• 搜索关键词过于具体\n• 相关新闻较少\n• 网络连接问题\n\n建议尝试使用更通用的关键词重新搜索。`;
                        } else if (outputText.includes('validation error')) {
                            content = `数据格式验证失败，这通常是服务器端的问题。\n\n技术详情: ${outputText}`;
                        } else {
                            content = `错误信息: ${outputText}`;
                        }

                        toolResultComponents += createToolCollapsible('result', title, content, toolName);
                    } else {
                        // 创建工具结果组件
                        const title = `工具执行完成: ${toolName}`;
                        let content = '';

                        // 对于search_stock_news工具，直接使用JSON数据进行新闻展示
                        if (toolName === 'search_stock_news') {
                            content = `返回结果: ${outputText}`;
                        } else {
                            // 其他工具的处理逻辑
                            try {
                                const parsedData = JSON.parse(outputText);
                                if (Array.isArray(parsedData) && parsedData.length > 0) {
                                    content = `获取数据: ${parsedData.length} 条记录\n返回结果: ${outputText}`;
                                } else {
                                    content = `返回结果: ${outputText}`;
                                }
                            } catch (e) {
                                // 不是JSON，直接使用文本
                                content = `返回结果: ${outputText}`;
                            }
                        }

                        toolResultComponents += createToolCollapsible('result', title, content, toolName);
                    }
                } else {
                    const title = `工具执行完成: ${toolName}`;
                    const content = '执行完成，无返回数据';
                    toolResultComponents += createToolCollapsible('result', title, content, toolName);
                }
            }
        }
    }

    if (toolResultComponents) {
        // 将工具结果组件添加到上一个消息的内容中
        lastMessageContent.innerHTML += toolResultComponents;

        // 使用 requestAnimationFrame 确保DOM更新后再滚动
        requestAnimationFrame(() => {
            scrollToBottom();
        });

        return true; // 成功合并
    }

    return false; // 没有合并
}

// 检查是否可以合并到上一条消息（同一人的连续消息）
function canMergeWithLastMessage(data) {
    const messages = chatMessages.querySelectorAll('.message');
    if (messages.length === 0) {
        return false;
    }

    const lastMessage = messages[messages.length - 1];
    const lastSender = lastMessage.querySelector('.message-name')?.textContent?.trim();
    const currentSender = data.name;

    // 同一发送者且不是系统消息
    return lastSender === currentSender && data.role !== 'system';
}

// 合并消息到上一条
function mergeWithLastMessage(data) {
    const messages = chatMessages.querySelectorAll('.message');
    const lastMessage = messages[messages.length - 1];
    const lastMessageContent = lastMessage.querySelector('.message-content');

    if (!lastMessageContent) {
        return false;
    }

    // 处理新消息内容 - 使用与addMessage相同的逻辑
    let content = data.content || '';
    if (!content) {
        content = '<em>消息内容为空</em>';
    } else {
        // 先处理 Call mentions
        content = processCallMentions(content);

        // 检查是否包含工具调用格式
        const hasToolCalls = content.includes('🔧 **工具调用**:') || content.includes('✅ **工具执行完成**:');

        if (hasToolCalls) {
            console.log('✅ 合并消息检测到工具调用，进行特殊处理');
            // 先处理工具调用
            const processedContent = processToolCalls(content);
            // 对于包含HTML组件的内容，不要进行换行符替换
            content = processedContent;
        } else if (md) {
            try {
                content = md.render(content);
            } catch (error) {
                console.error('Markdown处理失败:', error);
                content = content.replace(/\n/g, '<br>');
            }
        } else {
            content = content.replace(/\n/g, '<br>');
        }
    }

    // 添加分隔线和新内容
    const continuationDiv = document.createElement('div');
    continuationDiv.className = 'message-continuation';
    continuationDiv.innerHTML = content;

    lastMessageContent.appendChild(continuationDiv);

    // 使用 requestAnimationFrame 确保DOM更新后再滚动
    requestAnimationFrame(() => {
        scrollToBottom();
    });

    return true;
}

// 添加消息到聊天界面
function addMessage(data, saveToHistory = true) {
    console.log('收到消息:', data);  // 调试信息

    // 检查消息类型并处理
    if (data.raw_content && Array.isArray(data.raw_content)) {
        const hasToolUse = data.raw_content.some(item =>
            item && typeof item === 'object' && item.type === 'tool_use'
        );
        const hasToolResult = data.raw_content.some(item =>
            item && typeof item === 'object' && item.type === 'tool_result'
        );

        // 如果是纯tool_result消息（没有文本内容），尝试合并到上一个消息
        if (hasToolResult && !hasToolUse && (!data.content || data.content.trim() === '')) {
            console.log('检测到纯tool_result消息，尝试合并到上一个消息');
            console.log('消息详情:', {
                name: data.name,
                role: data.role,
                content: data.content,
                hasToolResult: hasToolResult,
                hasToolUse: hasToolUse
            });
            if (mergeToolResultToLastMessage(data)) {
                console.log('✅ 合并成功，不创建新消息泡');
                return; // 成功合并，不创建新消息泡
            } else {
                console.log('❌ 合并失败，将创建新消息泡');
            }
        }

        // 如果是tool_use消息但content为空，需要从raw_content中提取内容
        if (hasToolUse && (!data.content || data.content.trim() === '')) {
            console.log('检测到tool_use消息，从raw_content提取内容');
            data.content = extractContentFromRawContent(data.raw_content);
        }

        // 对于历史消息，如果有raw_content且包含工具调用/结果，重新处理content
        // 这样可以确保历史消息和实时消息使用相同的处理逻辑
        if ((hasToolUse || hasToolResult) && saveToHistory === false) {
            console.log('🔄 历史消息包含工具调用/结果，重新从raw_content生成内容');
            data.content = extractContentFromRawContent(data.raw_content);
        }
    }

    // 检查是否可以合并到上一条消息（同一人的连续消息）
    if (canMergeWithLastMessage(data)) {
        console.log('检测到连续消息，尝试合并:', data.name);
        if (mergeWithLastMessage(data)) {
            console.log('✅ 连续消息合并成功');
            return; // 成功合并
        } else {
            console.log('❌ 连续消息合并失败');
        }
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${data.role}`;

    // 处理消息内容
    let content = data.content || '';
    console.log('原始内容:', JSON.stringify(content));

    // 保留原始内容，不进行trim处理
    // 这样可以保留开头的换行符，避免消息被"吞掉"
    console.log('保持原始内容不变');

    // 如果内容为空，显示提示
    if (!content) {
        content = '<em>消息内容为空</em>';
        console.warn('消息内容为空:', data);
    } else {
        try {
            // 先处理 Call mentions（在 Markdown 处理之前）
            content = processCallMentions(content);

            // 检查原始内容是否包含工具调用格式
            const hasToolCalls = content.includes('🔧 **工具调用**:') || content.includes('✅ **工具执行完成**:');

            // 特殊处理：如果内容以换行符开头，进行预处理
            let contentForMarkdown = content;
            let hasLeadingNewline = false;

            if (content.startsWith('\n')) {
                hasLeadingNewline = true;
                // 移除开头的换行符进行Markdown解析，稍后会重新添加
                contentForMarkdown = content.substring(1);
                console.log('检测到开头换行符，临时移除进行解析');
                console.log('移除换行符后的内容:', contentForMarkdown.substring(0, 100) + '...');
            }

            let markdownResult;
            if (hasToolCalls) {
                console.log('✅ 检测到工具调用，先处理工具调用再进行Markdown');
                // 先处理工具调用
                const processedContent = processToolCalls(contentForMarkdown);
                // 对于包含HTML组件的内容，不要全局替换换行符，而是保持HTML结构
                markdownResult = processedContent;
            } else if (md) {
                console.log('使用markdown-it解析，输入内容长度:', contentForMarkdown.length);
                console.log('输入内容前100字符:', contentForMarkdown.substring(0, 100));
                markdownResult = md.render(contentForMarkdown);
                console.log('markdown-it解析结果长度:', markdownResult.length);
                console.log('解析结果前200字符:', markdownResult.substring(0, 200));
            } else {
                console.log('使用备用方案解析');
                // 备用方案：如果markdown-it不可用，使用简单的HTML转义
                markdownResult = contentForMarkdown.replace(/&/g, '&amp;')
                                                  .replace(/</g, '&lt;')
                                                  .replace(/>/g, '&gt;')
                                                  .replace(/"/g, '&quot;')
                                                  .replace(/'/g, '&#39;')
                                                  .replace(/\n/g, '<br>');
            }

            // 检查解析结果是否有效
            if (markdownResult && markdownResult.trim() !== '' && markdownResult.trim() !== '<p></p>') {
                content = markdownResult;

                // 工具调用已经在Markdown处理前处理过了，这里不再处理

                // 如果原来有开头换行符，在解析结果前添加换行
                if (hasLeadingNewline) {
                    content = '<br>' + content;
                    console.log('重新添加开头换行符');
                }
            } else {
                // 如果Markdown解析结果为空，使用原始内容并进行HTML转义
                content = content.replace(/&/g, '&amp;')
                              .replace(/</g, '&lt;')
                              .replace(/>/g, '&gt;')
                              .replace(/"/g, '&quot;')
                              .replace(/'/g, '&#39;')
                              .replace(/\n/g, '<br>');
                console.log('使用HTML转义后的内容:', content);
            }
        } catch (error) {
            console.error('Markdown解析错误:', error);
            // 如果解析失败，使用原始内容并进行HTML转义
            content = content.replace(/&/g, '&amp;')
                          .replace(/</g, '&lt;')
                          .replace(/>/g, '&gt;')
                          .replace(/"/g, '&quot;')
                          .replace(/'/g, '&#39;')
                          .replace(/\n/g, '<br>');
        }
    }

    messageDiv.innerHTML = `
        <div class="message-header">
            <div class="message-avatar">${data.icon}</div>
            <span class="message-name">${data.name}</span>
            <span class="message-time">${data.timestamp}</span>
        </div>
        <div class="message-content">${content}</div>
    `;
    
    // 在添加消息前检查是否在底部
    const wasAtBottom = chatMessages.scrollHeight - chatMessages.clientHeight <= chatMessages.scrollTop + 5;

    chatMessages.appendChild(messageDiv);

    // 添加动画效果
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(20px)';

    requestAnimationFrame(() => {
        messageDiv.style.transition = 'all 0.3s ease';
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';

        // 在动画开始后立即滚动到底部（如果用户之前在底部）
        if (wasAtBottom) {
            scrollToBottom(false, true); // 强制滚动到底部
        } else {
            // 如果用户不在底部，显示新消息指示器
            showNewMessageIndicator();
        }
    });

    // 如果需要保存到历史，则添加到当前会话（只保存会话基本信息）
    // 只有在有有效会话ID时才创建历史记录，避免创建id为null的记录
    if (saveToHistory && currentSessionId) {
        const currentSession = chatHistory.find(s => s.id === currentSessionId);
        if (!currentSession) {
            // 生成会话标题（使用第一条用户消息）
            let sessionTitle = '新对话';
            if (data.role === 'user' && data.content) {
                sessionTitle = data.content.substring(0, 30) + (data.content.length > 30 ? '...' : '');
            }

            chatHistory.push({
                id: currentSessionId,
                title: sessionTitle,
                timestamp: new Date().toISOString()
            });
            saveChatHistory();
            renderHistoryList();
        }
    }
}

// 滚动到聊天底部的函数
function scrollToBottom(smooth = false, force = false) {
    // 检查用户是否在查看历史消息（不在底部）
    // 增加容差值，确保更准确的检测
    const scrollTop = chatMessages.scrollTop;
    const scrollHeight = chatMessages.scrollHeight;
    const clientHeight = chatMessages.clientHeight;
    const isAtBottom = scrollHeight - clientHeight <= scrollTop + 10;

    // 如果不是强制滚动且用户不在底部，则不滚动
    if (!force && !isAtBottom) {
        showNewMessageIndicator();
        return;
    }

    if (smooth) {
        chatMessages.scrollTo({
            top: scrollHeight,
            behavior: 'smooth'
        });
    } else {
        chatMessages.scrollTop = scrollHeight;
    }

    // 隐藏新消息指示器
    hideNewMessageIndicator();
}

// 显示新消息指示器
function showNewMessageIndicator() {
    let indicator = document.getElementById('new-message-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'new-message-indicator';
        indicator.className = 'new-message-indicator';
        indicator.innerHTML = `
            <i class="fas fa-arrow-down"></i>
            <span>有新消息</span>
        `;
        indicator.onclick = () => scrollToBottom(true, true);
        chatMessages.parentElement.appendChild(indicator);
    }
    indicator.style.display = 'flex';
}

// 隐藏新消息指示器
function hideNewMessageIndicator() {
    const indicator = document.getElementById('new-message-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

// 监听滚动事件，当用户滚动到底部时隐藏指示器
function setupScrollListener() {
    chatMessages.addEventListener('scroll', () => {
        // 使用与 scrollToBottom 相同的检测逻辑
        const scrollTop = chatMessages.scrollTop;
        const scrollHeight = chatMessages.scrollHeight;
        const clientHeight = chatMessages.clientHeight;
        const isAtBottom = scrollHeight - clientHeight <= scrollTop + 10;

        if (isAtBottom) {
            hideNewMessageIndicator();
        }
    });
}

// 更新连接状态
function updateConnectionStatus(status, text) {
    connectionStatus.className = `connection-status ${status}`;
    connectionStatus.innerHTML = `<i class="fas fa-circle"></i> ${text}`;
}

// 更新状态文本
function updateStatus(message) {
    // statusText.textContent = message; // This line was removed as per the new_code
    
    // 添加动画效果
    // statusText.style.transform = 'scale(1.05)'; // This line was removed as per the new_code
    // setTimeout(() => { // This line was removed as per the new_code
    //     statusText.style.transform = 'scale(1)'; // This line was removed as per the new_code
    // }, 200); // This line was removed as per the new_code
}

// 显示/隐藏加载动画
function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

// 显示/隐藏加载覆盖层
function showLoadingOverlay(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function hideLoadingOverlay() {
    loadingOverlay.style.display = 'none';
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    notificationContainer.appendChild(notification);
    
    // 自动移除通知
    setTimeout(() => {
        notification.style.transition = 'all 0.3s ease';
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// 获取通知图标
function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

// 处理 [Call @xxx] 格式
function processCallMentions(content) {
    // 匹配 [Call @xxx] 格式，支持中文和英文名称
    const callPattern = /\[Call @([^\]]+)\]/g;

    return content.replace(callPattern, function(_, name) {
        return `<span class="call-mention">@${name}</span>`;
    });
}

// 处理原始工具内容（JSON格式）
function processRawToolContent(rawContent) {
    console.log('🔍 处理原始工具内容:', rawContent);

    let toolComponents = '';

    rawContent.forEach(item => {
        if (item.type === 'tool_use') {
            // 处理工具调用
            const title = `工具调用: ${item.name}`;
            const content = `参数: ${JSON.stringify(item.input, null, 2)}`;
            toolComponents += createToolCollapsible('call', title, content, item.name);
            console.log('🔧 创建工具调用组件:', { name: item.name, id: item.id });

        } else if (item.type === 'tool_result') {
            // 处理工具结果
            const title = `工具执行完成: ${item.name}`;
            let content = '';

            if (item.output && Array.isArray(item.output)) {
                item.output.forEach(output => {
                    if (output.type === 'text') {
                        content += output.text;
                    }
                });
            }

            toolComponents += createToolCollapsible('result', title, content, item.name);
            console.log('✅ 创建工具结果组件:', { name: item.name, id: item.id });
        }
    });

    return toolComponents || null;
}

// 提取工具结果部分（只提取工具结果，不包含工具调用）
function extractToolResults(content) {
    console.log('🔍 提取工具结果，内容长度:', content.length);

    // 匹配所有工具结果
    const resultPattern = /✅ \*\*工具执行完成\*\*:[^]*?(?=🔧 \*\*工具调用\*\*:|$)/g;
    const results = content.match(resultPattern);

    if (results && results.length > 0) {
        console.log('🔍 找到工具结果:', results.length, '个');
        return results.join('\n\n');
    }

    console.log('🔍 未找到工具结果');
    return null;
}

// 提取新增的工具结果（只处理工具结果，不处理已存在的工具调用）
function extractNewResults(newContent, existingContent) {
    console.log('🔍 提取新增结果，新内容长度:', newContent.length);
    console.log('🔍 现有内容长度:', existingContent.length);
    console.log('🔍 新内容预览:', newContent.substring(0, 300) + '...');

    // 更精确的工具结果匹配：从工具执行完成开始到内容结束
    const resultPattern = /✅ \*\*工具执行完成\*\*:[^]*$/g;
    const results = newContent.match(resultPattern);

    if (results && results.length > 0) {
        console.log('🔍 找到工具结果:', results.length, '个');
        console.log('🔍 工具结果内容:', results[0].substring(0, 200) + '...');

        // 处理所有工具结果
        let processedResults = '';
        results.forEach(result => {
            processedResults += processToolCalls(result);
        });
        return processedResults;
    }

    console.log('🔍 未找到新的工具结果');
    return null;
}

// 创建工具折叠组件
function createToolCollapsible(type, title, content, toolName = '') {
    const id = `tool-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const typeClass = type === 'call' ? 'tool-call-collapsible' : 'tool-result-collapsible';
    const icon = type === 'call' ? '🔧' : '✅';

    // 清理内容，移除多余的空白字符
    let cleanContent = content.trim();

    // 解析内容，分离属性和值
    let formattedContent = '';

    if (type === 'call') {
        // 工具调用：解析参数
        if (cleanContent.startsWith('参数: ')) {
            const paramStr = cleanContent.substring(4); // 移除 "参数: " 前缀
            formattedContent = `
                <div class="tool-property">
                    <div class="property-label">参数</div>
                    <div class="property-value">
                        <pre><code>${escapeHtml(paramStr)}</code></pre>
                    </div>
                </div>`;
        } else {
            formattedContent = `<div class="tool-content-text">${escapeHtml(cleanContent)}</div>`;
        }
    } else {
        // 工具结果：特殊处理search_stock_news
        if (toolName === 'search_stock_news') {
            // 检查是否是JSON格式的新闻数据
            if (cleanContent.startsWith('[') && cleanContent.endsWith(']')) {
                formattedContent = createNewsResultDisplay(cleanContent);
            } else {
                // 解析属性格式的新闻数据
                const lines = cleanContent.split('\n');
                let newsDataLine = '';

                for (let line of lines) {
                    if (line.includes('返回结果: ') && line.includes('[{')) {
                        newsDataLine = line.substring(line.indexOf('[{'));
                        break;
                    } else if (line.includes('返回结果: ') && line.includes('[')) {
                        // 处理可能的JSON数组格式
                        const jsonStart = line.indexOf('[');
                        if (jsonStart !== -1) {
                            newsDataLine = line.substring(jsonStart);
                            break;
                        }
                    }
                }

                if (newsDataLine) {
                    // 尝试找到完整的JSON字符串
                    let fullJsonData = newsDataLine;

                    // 如果JSON数据跨多行，需要拼接
                    if (!newsDataLine.endsWith(']')) {
                        for (let i = lines.indexOf(lines.find(l => l.includes(newsDataLine))) + 1; i < lines.length; i++) {
                            fullJsonData += lines[i];
                            if (lines[i].includes(']')) {
                                break;
                            }
                        }
                    }

                    formattedContent = createNewsResultDisplay(fullJsonData);
                } else {
                    // 如果没有找到JSON数据，使用默认处理
                    formattedContent = parseToolResultProperties(cleanContent);
                }
            }
        } else {
            // 其他工具结果：解析多个属性
            formattedContent = parseToolResultProperties(cleanContent, toolName);
        }
    }

    console.log(`🔧 创建${type}组件:`, { title, toolName, contentLength: cleanContent.length, contentPreview: cleanContent.substring(0, 100) });

    return `<div class="tool-collapsible ${typeClass}">
            <div class="tool-header" onclick="toggleToolContent('${id}')">
                <span>
                    <span class="tool-icon">${icon}</span>
                    ${title}
                </span>
                <span class="tool-toggle" id="toggle-${id}">▼</span>
            </div>
            <div class="tool-content" id="content-${id}">
                ${formattedContent}
            </div>
        </div>`;
}

// 解析工具结果属性
function parseToolResultProperties(cleanContent, toolName = '') {
    const lines = cleanContent.split('\n');
    let currentProperty = '';
    let currentValue = '';
    let formattedContent = '';

    for (let line of lines) {
        if (line.includes(': ')) {
            // 如果有之前的属性，先添加到结果中
            if (currentProperty) {
                // 特殊处理search_stock_news工具的返回结果
                if (toolName === 'search_stock_news' && currentProperty === '返回结果') {
                    // 尝试解析JSON新闻数据
                    try {
                        // 检查是否是JSON格式
                        if (currentValue.trim().startsWith('[') && currentValue.trim().includes('{')) {
                            formattedContent += createNewsResultDisplay(currentValue.trim());
                        } else {
                            formattedContent += createPropertyBlock(currentProperty, currentValue);
                        }
                    } catch (error) {
                        console.error('解析新闻数据失败:', error);
                        formattedContent += createPropertyBlock(currentProperty, currentValue);
                    }
                } else {
                    formattedContent += createPropertyBlock(currentProperty, currentValue);
                }
            }

            // 解析新属性
            const colonIndex = line.indexOf(': ');
            currentProperty = line.substring(0, colonIndex);
            currentValue = line.substring(colonIndex + 2);
        } else if (currentProperty && line.trim()) {
            // 继续当前属性的值
            currentValue += '\n' + line;
        }
    }

    // 添加最后一个属性
    if (currentProperty) {
        // 特殊处理search_stock_news工具的返回结果
        if (toolName === 'search_stock_news' && currentProperty === '返回结果') {
            // 尝试解析JSON新闻数据
            try {
                // 检查是否是JSON格式
                if (currentValue.trim().startsWith('[') && currentValue.trim().includes('{')) {
                    formattedContent += createNewsResultDisplay(currentValue.trim());
                } else {
                    formattedContent += createPropertyBlock(currentProperty, currentValue);
                }
            } catch (error) {
                console.error('解析新闻数据失败:', error);
                formattedContent += createPropertyBlock(currentProperty, currentValue);
            }
        } else {
            formattedContent += createPropertyBlock(currentProperty, currentValue);
        }
    }

    // 如果没有解析到属性，直接显示内容
    if (!formattedContent) {
        // 对于工具结果，如果内容包含Markdown格式，进行渲染
        if (md && (cleanContent.includes('##') || cleanContent.includes('#') || cleanContent.includes('**'))) {
            try {
                formattedContent = `<div class="tool-content-markdown">${md.render(cleanContent)}</div>`;
            } catch (error) {
                console.error('工具结果Markdown渲染失败:', error);
                formattedContent = `<div class="tool-content-text">${escapeHtml(cleanContent).replace(/\n/g, '<br>')}</div>`;
            }
        } else {
            formattedContent = `<div class="tool-content-text">${escapeHtml(cleanContent).replace(/\n/g, '<br>')}</div>`;
        }
    }

    return formattedContent;
}

// 创建属性块
function createPropertyBlock(property, value) {
    return `
        <div class="tool-property">
            <div class="property-label">${escapeHtml(property)}</div>
            <div class="property-value">
                <pre><code>${escapeHtml(value)}</code></pre>
            </div>
        </div>`;
}

// 创建新闻结果展示
function createNewsResultDisplay(newsData) {
    try {
        const newsArray = JSON.parse(newsData);
        if (!Array.isArray(newsArray) || newsArray.length === 0) {
            return `<div class="news-empty">暂无新闻数据</div>`;
        }

        let newsHtml = '<div class="news-results">';
        newsArray.forEach((news, index) => {
            const title = news.标题 || news.title || '无标题';
            const content = news.内容 || news.content || '无内容';
            const link = news.链接 || news.link || news.url || '#';

            newsHtml += `
                <div class="news-item">
                    <div class="news-header">
                        <h4 class="news-title">
                            ${link !== '#' ?
                                `<a href="${escapeHtml(link)}" target="_blank" rel="noopener noreferrer">${escapeHtml(title)}</a>` :
                                escapeHtml(title)
                            }
                        </h4>
                        <span class="news-index">#${index + 1}</span>
                    </div>
                    <div class="news-content">
                        ${escapeHtml(content)}
                    </div>
                    ${link !== '#' ? `
                        <div class="news-link">
                            <a href="${escapeHtml(link)}" target="_blank" rel="noopener noreferrer">
                                <i class="fas fa-external-link-alt"></i> 查看原文
                            </a>
                        </div>
                    ` : ''}
                </div>
            `;
        });
        newsHtml += '</div>';

        return newsHtml;
    } catch (error) {
        console.error('解析新闻数据失败:', error);
        return `<div class="news-error">新闻数据解析失败: ${escapeHtml(newsData)}</div>`;
    }
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 切换工具内容显示
function toggleToolContent(id) {
    console.log('🔄 切换工具内容:', id);
    const content = document.getElementById(`content-${id}`);
    const toggle = document.getElementById(`toggle-${id}`);

    if (content && toggle) {
        const isExpanded = content.classList.contains('expanded');
        console.log('当前状态:', isExpanded ? '展开' : '折叠');

        if (isExpanded) {
            content.classList.remove('expanded');
            toggle.classList.remove('expanded');
            console.log('✅ 已折叠');
        } else {
            content.classList.add('expanded');
            toggle.classList.add('expanded');
            console.log('✅ 已展开');
        }
    } else {
        console.log('❌ 未找到元素:', { content: !!content, toggle: !!toggle });
    }
}

// 确保函数在全局作用域
window.toggleToolContent = toggleToolContent;

// 处理工具调用的特殊格式 - 重写以正确处理复杂内容
function processToolCalls(content) {
    console.log('🔧 开始处理工具调用，原始内容:', content.substring(0, 200) + '...');


    // 将内容按段落分割，逐段处理
    const paragraphs = content.split('\n\n');
    const processedParagraphs = [];

    for (let i = 0; i < paragraphs.length; i++) {
        let paragraph = paragraphs[i];

        // 处理工具调用格式 - 使用全局替换而不是单次匹配
        const toolCallPattern = /🔧 \*\*工具调用\*\*: ([^\n]+)\n📋 \*\*参数\*\*: `([^`]*)`/g;
        paragraph = paragraph.replace(toolCallPattern, function(_, toolName, params) {
            console.log('🔧 处理工具调用:', { toolName, params: params.substring(0, 50) + '...' });

            const title = `工具调用: ${toolName}`;
            const toolContent = `参数: ${params}`;
            return createToolCollapsible('call', title, toolContent, toolName);
        });

        // 处理工具结果格式 - 匹配多行数据预览内容
        const toolResultPattern = /✅ \*\*工具执行完成\*\*: ([^\n]+)\n📊 \*\*获取数据\*\*: ([^\n]+)\n📄 \*\*数据预览\*\*: ([^]*?)(?=\n\n|$)/g;
        paragraph = paragraph.replace(toolResultPattern, function(_, toolName, dataInfo, preview) {
            console.log('✅ 处理工具结果:', { toolName, dataInfo, preview: preview.substring(0, 100) + '...' });

            const title = `工具执行完成: ${toolName}`;
            const toolContent = `获取数据: ${dataInfo}\n数据预览: ${preview}`;
            return createToolCollapsible('result', title, toolContent, toolName);
        });

        // 处理简单工具结果格式 - 使用全局替换
        const simpleResultPattern = /✅ \*\*工具执行完成\*\*: ([^\n]+)\n📊 \*\*返回结果\*\*: ([^]*?)(?=\n\n|$)/g;
        paragraph = paragraph.replace(simpleResultPattern, function(_, toolName, result) {
            console.log('✅ 处理简单工具结果:', { toolName, result: result.substring(0, 50) + '...' });

            const title = `工具执行完成: ${toolName}`;
            const toolContent = `返回结果: ${result}`;
            return createToolCollapsible('result', title, toolContent, toolName);
        });

        processedParagraphs.push(paragraph);
    }

    const result = processedParagraphs.join('\n\n');
    console.log('🔧 工具处理完成，最终内容:', result.substring(0, 200) + '...');
    return result;
}

// 工具函数：格式化时间
function formatTime(date) {
    return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript错误:', e.error);
    showNotification('发生了一个错误，请刷新页面重试', 'error');
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (socket) {
        socket.disconnect();
    }
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时的处理
        console.log('页面隐藏');
    } else {
        // 页面显示时的处理
        console.log('页面显示');
        if (!isConnected && socket) {
            socket.connect();
        }
    }
});

// 网络状态变化处理
window.addEventListener('online', function() {
    showNotification('网络连接已恢复', 'success');
    if (!isConnected && socket) {
        socket.connect();
    }
});

window.addEventListener('offline', function() {
    showNotification('网络连接已断开', 'error');
});
