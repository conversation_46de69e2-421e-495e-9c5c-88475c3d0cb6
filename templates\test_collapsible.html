<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具折叠测试</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <h1>工具折叠样式测试</h1>
        
        <h2>1. 工具调用测试</h2>
        <div class="tool-collapsible tool-call-collapsible">
            <div class="tool-header" onclick="toggleToolContent('test-tool-1')">
                <span>
                    <span class="tool-icon">🔧</span>
                    工具调用: search_stock_news
                </span>
                <span class="tool-toggle" id="toggle-test-tool-1">▼</span>
            </div>
            <div class="tool-content" id="content-test-tool-1">
参数: {"query": "人工智能 题材 逻辑 分支 潜力 2025"}
            </div>
        </div>
        
        <h2>2. 工具结果测试</h2>
        <div class="tool-collapsible tool-result-collapsible">
            <div class="tool-header" onclick="toggleToolContent('test-result-1')">
                <span>
                    <span class="tool-icon">✅</span>
                    工具执行完成: search_stock_news
                </span>
                <span class="tool-toggle" id="toggle-test-result-1">▼</span>
            </div>
            <div class="tool-content" id="content-test-result-1">
获取数据: 5 条记录
数据预览: 人工智能概念股午后全面爆发...
            </div>
        </div>
        
        <h2>3. Call mention测试</h2>
        <p>这是一个 <span class="call-mention">@Ken</span> 的测试</p>
        
        <h2>4. 混合测试</h2>
        <div class="message assistant">
            <div class="message-header">
                <span class="message-name">Leo</span>
                <span class="message-time">14:30:00</span>
            </div>
            <div class="message-content">
                <p>收到任务，我将立即检索人工智能题材的最新信息。</p>
                
                <div class="tool-collapsible tool-call-collapsible">
                    <div class="tool-header" onclick="toggleToolContent('test-mixed-1')">
                        <span>
                            <span class="tool-icon">🔧</span>
                            工具调用: search_stock_news
                        </span>
                        <span class="tool-toggle" id="toggle-test-mixed-1">▼</span>
                    </div>
                    <div class="tool-content" id="content-test-mixed-1">
参数: {"query": "人工智能 题材 逻辑 分支 潜力 2025"}
                    </div>
                </div>
                
                <p>正在处理中...</p>
            </div>
        </div>
        
        <h2>5. 直接HTML测试</h2>
        <p>如果上面的样式都正常显示，说明CSS工作正常。</p>
        <p>如果点击能正常展开/折叠，说明JavaScript也工作正常。</p>
        <p>那么问题就在主页面的HTML生成过程中。</p>
        
        <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
            <h3>调试信息</h3>
            <p>请检查：</p>
            <ul>
                <li>工具调用块是否显示为蓝色背景</li>
                <li>工具结果块是否显示为绿色背景</li>
                <li>@Ken 是否显示为蓝色标签</li>
                <li>点击工具块标题是否能展开/折叠</li>
                <li>Console中是否有错误信息</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 切换工具内容显示
        function toggleToolContent(id) {
            console.log('🔄 切换工具内容:', id);
            const content = document.getElementById(`content-${id}`);
            const toggle = document.getElementById(`toggle-${id}`);
            
            if (content && toggle) {
                const isExpanded = content.classList.contains('expanded');
                console.log('当前状态:', isExpanded ? '展开' : '折叠');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    toggle.classList.remove('expanded');
                    console.log('✅ 已折叠');
                } else {
                    content.classList.add('expanded');
                    toggle.classList.add('expanded');
                    console.log('✅ 已展开');
                }
            } else {
                console.log('❌ 未找到元素:', { content: !!content, toggle: !!toggle });
            }
        }
        
        // 确保函数在全局作用域
        window.toggleToolContent = toggleToolContent;
        
        console.log('✅ 工具折叠测试页面加载完成');
        
        // 页面加载完成后的测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 页面元素统计:');
            console.log('- tool-collapsible 元素数量:', document.querySelectorAll('.tool-collapsible').length);
            console.log('- tool-call-collapsible 元素数量:', document.querySelectorAll('.tool-call-collapsible').length);
            console.log('- tool-result-collapsible 元素数量:', document.querySelectorAll('.tool-result-collapsible').length);
            console.log('- call-mention 元素数量:', document.querySelectorAll('.call-mention').length);
        });
    </script>
</body>
</html>
