#!/usr/bin/env python3
"""
测试工具折叠功能
"""

import os
import sys
import json
import uuid
import time
from pathlib import Path
from datetime import datetime

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.system('chcp 65001 >nul 2>&1')
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

def create_tool_test_messages():
    """创建专门测试工具折叠的消息"""
    
    # 创建测试会话ID
    session_id = str(uuid.uuid4())
    message_file = Path("messages") / f"session_{session_id}.jsonl"
    
    print(f"创建工具测试消息文件: {message_file}")
    
    # 创建消息目录
    message_file.parent.mkdir(exist_ok=True)
    
    # 创建空文件
    with open(message_file, 'w', encoding='utf-8') as f:
        f.write("")
    
    # 测试消息
    test_messages = [
        # 1. 简单工具调用
        {
            'name': 'Ken',
            'content': '🔧 **工具调用**: search_stock_news\n📋 **参数**: `{"query": "人工智能", "limit": 5}`',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_tool_1'
        },
        
        # 2. 复杂参数的工具调用
        {
            'name': 'Leo',
            'content': '🔧 **工具调用**: get_stock_knowledge_data\n📋 **参数**: `{"keywords": ["AI芯片", "寒武纪", "海光信息"], "data_type": "financial", "time_range": "2024-01-01 to 2024-12-31", "include_analysis": true}`',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_tool_2'
        },
        
        # 3. 简单工具结果
        {
            'name': 'system',
            'content': '✅ **工具执行完成**: search_stock_news\n📊 **返回结果**: 找到5条相关新闻',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_result_1'
        },
        
        # 4. 复杂工具结果
        {
            'name': 'system',
            'content': '✅ **工具执行完成**: get_stock_knowledge_data\n📊 **获取数据**: 25 条记录\n📄 **数据预览**: 包含财务数据、技术指标、行业对比等详细信息，涵盖AI芯片行业的完整分析报告',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_result_2'
        },
        
        # 5. 最简单的工具结果
        {
            'name': 'system',
            'content': '✅ **工具执行完成**: update_database',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_result_3'
        },
        
        # 6. 包含工具调用的普通消息
        {
            'name': 'Ken',
            'content': '我需要检索一些数据：\n\n🔧 **工具调用**: search_market_data\n📋 **参数**: `{"market": "A股", "sector": "人工智能", "period": "1M"}`\n\n请稍等，正在处理...',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_mixed_1'
        },
        
        # 7. 包含工具结果的普通消息
        {
            'name': 'Leo',
            'content': '数据检索完成：\n\n✅ **工具执行完成**: search_market_data\n📊 **获取数据**: 50 条记录\n📄 **数据预览**: A股人工智能板块近一个月表现数据\n\n基于这些数据，我发现了以下趋势...',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_mixed_2'
        },
        
        # 8. 完成消息
        {
            'name': 'System',
            'content': '✅ 工具折叠测试完成！\n\n预期效果：\n- 所有工具调用应该显示为可折叠的蓝色块\n- 所有工具结果应该显示为可折叠的绿色块\n- 点击可以展开/折叠查看详细信息\n- 混合消息中的工具部分也应该被正确处理',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'type': 'completion'
        }
    ]
    
    # 逐个写入消息
    for i, msg in enumerate(test_messages):
        print(f"写入工具测试消息 {i+1}: {msg['name']}")
        
        with open(message_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(msg, ensure_ascii=False) + '\n')
            f.flush()
        
        time.sleep(0.3)
    
    print(f"\n工具测试完成，消息文件: {message_file}")
    print(f"会话ID: {session_id}")
    
    return session_id

def update_debug_session_id(session_id):
    """更新app_file.py中的DEBUG_SESSION_ID"""
    
    app_file = "app_file.py"
    
    if not os.path.exists(app_file):
        print(f"❌ 文件不存在: {app_file}")
        return False
    
    # 读取文件内容
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换DEBUG_SESSION_ID
    import re
    new_content = re.sub(
        r'DEBUG_SESSION_ID = "[^"]*"',
        f'DEBUG_SESSION_ID = "{session_id}"',
        content
    )
    
    # 写回文件
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ 已更新 DEBUG_SESSION_ID = \"{session_id}\"")
    return True

if __name__ == "__main__":
    print("🔧 创建工具折叠功能测试数据")
    print("=" * 50)
    
    session_id = create_tool_test_messages()
    
    if update_debug_session_id(session_id):
        print("\n🚀 工具测试准备完成！")
        print("\n启动步骤：")
        print("1. 确保 DEBUG_MODE = True")
        print("2. 运行: python app_file.py")
        print("3. 打开浏览器访问: http://localhost:5000")
        print("4. 打开开发者工具查看Console输出")
        print("5. 点击'初始化系统'")
        print("6. 输入任意消息开始测试")
        print("\n📝 预期效果：")
        print("- 工具调用显示为蓝色可折叠块")
        print("- 工具结果显示为绿色可折叠块")
        print("- 点击标题栏可以展开/折叠")
        print("- Console中会显示详细的处理日志")
        print("\n🔍 调试提示：")
        print("- 查看Console中的 '🔧 开始处理工具调用' 日志")
        print("- 查看 '🔧 工具调用匹配结果' 和 '✅ 复杂工具结果匹配' 日志")
        print("- 如果没有匹配，检查正则表达式是否正确")
    else:
        print("❌ 更新配置失败")
