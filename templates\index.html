<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票咨询代理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <!-- Markdown-it for better Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.2/dist/markdown-it.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> 股票咨询代理系统</h1>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 侧边栏 - 历史对话 -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <button id="toggleSidebar" class="toggle-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h3><i class="fas fa-history"></i> 历史对话</h3>
                    <button id="newChatBtn" class="new-chat-btn" title="新建对话">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                
                <div class="sidebar-content">
                    <div class="search-box">
                        <input type="text" id="searchHistory" placeholder="搜索历史对话...">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <div id="historyList" class="history-list">
                        <!-- 历史对话将通过JavaScript动态加载 -->
                        <div class="no-history">
                            <i class="fas fa-comment-slash"></i>
                            <p>暂无历史对话</p>
                            <p>开始新的对话吧！</p>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 聊天区域 -->
            <section class="chat-section">
                <div class="chat-header">
                    <h2><i class="fas fa-comments"></i> 股票咨询聊天</h2>
                    <div class="chat-actions">
                        <span class="session-info" id="sessionInfo">新会话</span>
                        <div class="connection-status" id="connectionStatus">
                            <i class="fas fa-circle"></i> 已连接
                        </div>
                    </div>
                </div>

                <div class="chat-container">
                    <div id="chatMessages" class="chat-messages">
                        <div class="welcome-message">
                            <div class="welcome-content">
                                <i class="fas fa-hand-wave"></i>
                                <h3>欢迎使用股票咨询代理系统！</h3>
                                <p>您可以直接输入您想了解的股票题材或相关问题，我们的专家团队将为您提供专业分析。</p>
                                <div class="quick-examples">
                                    <span class="example-tag" data-text="请分析人工智能题材">人工智能题材</span>
                                    <span class="example-tag" data-text="分析新能源汽车板块">新能源汽车</span>
                                    <span class="example-tag" data-text="半导体行业最新动态">半导体行业</span>
                                    <span class="example-tag" data-text="医药生物题材机会">医药生物</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chat-input-container">
                        <div class="input-wrapper">
                            <input type="text" id="messageInput" placeholder="请输入您想分析的题材或问题...">
                            <button id="sendBtn" class="send-btn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="input-hint">
                            <i class="fas fa-info-circle"></i>
                            按 Enter 发送消息，Shift + Enter 换行
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-content">
                <p><i class="fas fa-shield-alt"></i> 本系统仅供学习和研究使用，投资有风险，决策需谨慎</p>
                <div class="footer-links">
                    <span>Powered by Flask + WebSocket</span>
                    <span>|</span>
                    <span>AgentScope Multi-Agent System</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在初始化代理系统...</p>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container"></div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
