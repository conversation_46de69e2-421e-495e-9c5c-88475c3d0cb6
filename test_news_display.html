<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻展示测试</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
        <h1>新闻展示测试</h1>
        
        <div id="test-container"></div>
        
        <button onclick="testNewsDisplay()">测试新闻展示</button>
    </div>

    <script src="static/js/app.js"></script>
    <script>
        function testNewsDisplay() {
            const testData = '[{"标题":"AI芯片概念股大涨","内容":"人工智能芯片板块今日表现强劲，多只个股涨停。随着AI技术的快速发展，相关芯片需求持续增长。","链接":"http://example.com/news1"},{"标题":"新能源汽车销量创新高","内容":"本月新能源汽车销量再创历史新高，同比增长超过50%。政策支持和技术进步推动行业快速发展。","链接":"http://example.com/news2"},{"标题":"科技股集体上涨","内容":"今日科技股表现亮眼，板块整体上涨3.5%。投资者对科技创新前景保持乐观态度。","链接":"http://example.com/news3"}]';

            // 测试模拟真实的消息更新流程
            const mockData = {
                raw_content: [{
                    type: 'tool_result',
                    name: 'search_stock_news',
                    output: [{
                        type: 'text',
                        text: testData
                    }]
                }],
                content: '✅ **工具执行完成**: search_stock_news\n📊 **获取数据**: 3 条记录\n📄 **数据预览**: AI芯片概念股大涨...'
            };

            // 先创建一个工具调用
            const toolCallHtml = createToolCollapsible('call', '工具调用: search_stock_news', '参数: {"query": "AI芯片"}', 'search_stock_news');
            document.getElementById('test-container').innerHTML = toolCallHtml;

            // 模拟消息更新
            setTimeout(() => {
                console.log('🧪 测试模拟消息更新');
                updateLastMessage(mockData);
            }, 1000);
        }
        
        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
