import os
import sys
import json
import time
from pathlib import Path
from agentscope.agents import AgentBase,ReActAgentV2,DialogAgent, UserAgent
import agentscope
from agentscope.formatters import OpenAIFormatter
from agentscope.service import ServiceToolkit
from agentscope.message import Msg
from dotenv import load_dotenv
from agentscope import msghub
import re
from typing import Union, Any, Optional
from datetime import datetime

# 设置UTF-8编码
if sys.platform.startswith('win'):
    # Windows系统设置UTF-8编码
    os.system('chcp 65001 >nul 2>&1')
    # 重新配置stdout和stderr使用UTF-8
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

load_dotenv()
API_URL = os.getenv("API_URL")
API_KEY = os.getenv("API_KEY")

# 全局会话ID和消息文件路径
SESSION_ID = None
MESSAGE_FILE = None
WRITTEN_MESSAGES = set()  # 记录已写入的消息，避免重复

def pre_speak_hook(
    self: AgentBase,
    msg: Msg,
    stream: bool,
    last: bool,
) -> Union[Msg, None]:
    """钩子函数，将消息写入文件"""
    global SESSION_ID, MESSAGE_FILE, WRITTEN_MESSAGES

    try:
        if not SESSION_ID or not MESSAGE_FILE:
            print(f"Pre speak hook: {getattr(msg, 'name', 'Unknown')}")
            return None

        if not hasattr(msg, 'content') or not msg.content:
            return None

        # 提取消息信息
        agent_name = getattr(msg, 'name', 'Unknown')
        timestamp = datetime.now().strftime("%H:%M:%S")
        role = getattr(msg, 'role', 'assistant')

        # 处理所有类型的消息内容
        content_str = ""
        

        if isinstance(msg.content, str):
            content_str = msg.content
        elif isinstance(msg.content, list):
            for item in msg.content:
                if isinstance(item, dict):
                    item_type = item.get('type', '')
                    if item_type == 'text':
                        # 替换[@ref_msg_id2]
                        content_str += item.get('text', '')
                    # 不在这里处理tool_use和tool_result，让前端处理
                elif hasattr(item, 'text'):
                    content_str += item.text
        raw_content = msg.content
        # 使用正则替换各种引用信息
        # 移除[@ref_msg_id2]这类消息
        content_str = re.sub(r'\[@ref_msg_id\d+\]', '', content_str)
        # 移除[本则信息发言id]ref_msg_id14这类消息（整行移除）
        content_str = re.sub(r'\n?\[本则信息发言id\]ref_msg_id\d+', '', content_str)
        # 移除[ref_doc_id:xxx]这类引用信息
        content_str = re.sub(r'\[ref_doc_id:[^\]]+\]', '', content_str)

        # 对raw_content也进行相同的正则替换处理
        if isinstance(raw_content, str):
            raw_content = re.sub(r'\[@ref_msg_id\d+\]', '', raw_content)
            raw_content = re.sub(r'\n?\[本则信息发言id\]ref_msg_id\d+', '', raw_content)
            raw_content = re.sub(r'\[ref_doc_id:[^\]]+\]', '', raw_content)
        elif isinstance(raw_content, list):
            # 如果raw_content是列表，需要处理其中的文本内容
            processed_raw_content = []
            for item in raw_content:
                if isinstance(item, dict):
                    processed_item = item.copy()
                    if item.get('type') == 'text' and 'text' in item:
                        text_content = item['text']
                        text_content = re.sub(r'\[@ref_msg_id\d+\]', '', text_content)
                        text_content = re.sub(r'\n?\[本则信息发言id\]ref_msg_id\d+', '', text_content)
                        text_content = re.sub(r'\[ref_doc_id:[^\]]+\]', '', text_content)
                        processed_item['text'] = text_content
                    processed_raw_content.append(processed_item)
                else:
                    processed_raw_content.append(item)
            raw_content = processed_raw_content

        # 生成消息唯一标识，用于去重
        message_hash = hash(str(raw_content) + agent_name + timestamp)

        # 检查是否已经写入过这条消息
        if message_hash in WRITTEN_MESSAGES:
            print(f"消息已存在，跳过: {agent_name} - {content_str[:30]}...")
            return None

        # 记录消息已写入
        WRITTEN_MESSAGES.add(message_hash)

        # 构造消息对象，包含完整的原始内容
        message_info = {
            'name': agent_name,
            'content': content_str,
            'role': role,
            'timestamp': timestamp,
            'raw_content': raw_content,
            'session_id': SESSION_ID,
            'message_id': f"{agent_name}_{timestamp}_{message_hash}"
        }

        # 写入消息文件
        write_message_to_file(message_info)

        print(f"消息已写入文件: {agent_name} - {content_str[:50]}...")

    except Exception as e:
        print(f"钩子函数错误: {e}")
        print(f"Pre speak hook: {getattr(msg, 'name', 'Unknown')}")

    return None

def write_message_to_file(message_info):
    """将消息写入文件"""
    try:
        # 使用追加模式写入文件，确保编码安全
        with open(MESSAGE_FILE, 'a', encoding='utf-8', errors='ignore') as f:
            # 确保JSON序列化时处理特殊字符
            json_str = json.dumps(message_info, ensure_ascii=False, default=str)
            f.write(json_str + '\n')
            f.flush()  # 立即刷新到磁盘
            print(f"✅ 消息已写入文件: {message_info.get('name', 'Unknown')}")
    except Exception as e:
        print(f"❌ 写入消息文件失败: {e}")
        import traceback
        traceback.print_exc()


AgentBase.register_class_hook(
    "pre_speak",
    "customized_pre_speak_hook",
    pre_speak_hook,
)


def call_agent(msg, memory, memory_map, agent_names):
    content = msg.content
    # 如果没有@，则默认是将信息回传给会话发起者
    if '[Call @' not in content and ('Bye!' in content or 'Exit!' in content):
        return None
    # [Call @Ken]
    pattern = r'\[Call @(\w+)\]'  # 匹配 @名字:内容
    match = re.findall(pattern, msg.content)
    fix_content = msg.content  # 提前定义fix_content变量
    if len(match) == 0:
        now_user = msg.name
        # 如果是Leo没有@，则默认是将信息回传给上一个发言人
        if now_user == 'Leo':
            fix_name = memory[-1].name
        # 如果是Lus没有@，则默认是将信息回传给Ken
        elif now_user == 'Lus':
            fix_name = 'Ken'
        else:
            return None
    else:
        fix_name = match[-1]
        if fix_name not in ['Leo', 'Morgan', 'Ken', 'Lus', 'Jess']:
            return "Error: 被@的人不存在，请仔细检查"
    
    temp_agent = agent_names[fix_name]
    # 为Jess灌入全部的记忆
    if fix_name == 'Jess':
        temp_agent.memory.clear()
        # 为Jess添加memory
        for memory_temp in memory:
            temp_agent.memory.add(memory_temp)
    # 当调用Leo时，清除Leo的记忆
    elif fix_name == 'Leo':
        temp_agent.memory.clear()


    if '[@ref_msg_id' in fix_content:
        # 正则取出引用的id
        pattern = r'\[@ref_msg_id(\d+)\]'
        match = re.findall(pattern, fix_content)
        if len(match) == 0:
            return "Error: 请遵循Ref Format"
        # 去重
        match = list(set(match))
        agent_memory = agent_names[fix_name].memory
        for ref_id in match:
            print(f"引用发言@ref_msg_id{ref_id}的内容")
            ref_msg = memory[int(ref_id)-1]
            agent_memory.add(ref_msg)

    fix_msg = Msg(
        name=msg.name,
        content=f"来自{msg.name}的消息：\n{fix_content}",
        role="user",
    )
    # 统一使用原id
    memory_map[fix_msg.id] = msg.id
    # 清理agent记忆
    memory_deduplication(fix_name, memory_map, agent_names)
    return agent_names[fix_name](fix_msg)

def loop_call(msg, memory, memory_map, agent_names):
    new_msg = call_agent(add_ref_id(msg, memory), memory, memory_map, agent_names)
    if new_msg is None:
        return None
    if 'Err' in new_msg:
        print("Error: ", new_msg, "正在进行重试")
        # 回退到上一次的msg
        memory = memory[:-1]
        msg = memory[-1]
        return loop_call(msg, memory, memory_map, agent_names)
        
    if isinstance(new_msg.content, list):
        fix_content = ""
        for item in new_msg.content:
            if item['type'] == 'text':
                fix_content += item['text']
        new_msg.content = fix_content
        
    memory.append(new_msg)
    if '@' not in new_msg.content:
        last_caller = msg.name
        new_msg.content = f"来自@{last_caller}的信息：\n{new_msg.content}"
    if  'Bye!' in new_msg.content or 'Exit!' in new_msg.content:
        return None
    return loop_call(new_msg, memory, memory_map, agent_names)

def add_ref_id(msg, memory):
    """
    在每条信息后添加[本则信息发言id]ref_msg_idxxx的格式，xxx为该信息在memory中的id
    """
    if msg.role == 'user':
        return msg
    if isinstance(msg.content, list):
        fix_content = ""
        for item in msg.content:
            if item['type'] == 'text':
                fix_content += item['text']
        msg.content = fix_content
    if '[本则信息发言id]ref_msg_id' in msg.content:
        # 正则移除这行
        pattern = r'\[发言id\]ref_msg_id\d+'
        msg.content = re.sub(pattern, '', msg.content)
        msg.content += f'[本则信息发言id]ref_msg_id{len(memory)}'
    else:
        msg.content += f'\n\n[本则信息发言id]ref_msg_id{len(memory)}'
    return msg

def memory_deduplication(agent_name, memory_map, agent_names):
    # memory去重
    agent_memory = agent_names[agent_name].memory
    save_memory_id = [] # 当前已经存在的memory_id
    wait_del_id = [] # 需要删除的memory_id
    memory_id = 0
    for memory_data in agent_memory.get_memory():
        _id = memory_data.id
        _map_id = memory_map.get(_id, None)
        if _map_id is None:
            # # 如果没有映射，且id不在save_memory_id中，则添加到save_memory_id
            if _id not in save_memory_id:
                save_memory_id.append(_id)
            elif _id in save_memory_id:
                wait_del_id.append(memory_id)
        else:
            if _map_id in save_memory_id:
                wait_del_id.append(memory_id)
            elif _map_id not in save_memory_id:
                save_memory_id.append(_map_id)
        memory_id += 1

def init_agent():
    # Load model configs
    agentscope.init(
        model_configs=[
            {
                "config_name": "思考者",
                "client_args": {
                    # specify the base URL of the API
                    "base_url": os.getenv("API_URL")
                },
                "api_key": os.environ.get("API_KEY"),
                "model_type": "openai_chat",
                "model_name": "deepseek-r1",
                "max_length": 128000,
                # When using ReActAgentV2, streaming (i.e., setting "stream": True)
                # is not supported.
                # "stream": False,
            },
            {
                "config_name": "规划师",
                "client_args": {
                    # specify the base URL of the API
                    "base_url": os.getenv("API_URL")
                },
                "api_key": os.environ.get("API_KEY"),
                "model_type": "openai_chat",
                "model_name": "deepseek-v3",
                "max_length": 128000,
                # When using ReActAgentV2, streaming (i.e., setting "stream": True)
                # is not supported.
                # "stream": False,
            },
            {
                "config_name": "工具使用者",
                "client_args": {
                    # specify the base URL of the API
                    "base_url": os.getenv("API_URL")
                },
                "api_key": os.environ.get("API_KEY"),
                "model_type": "openai_chat",
                "model_name": "Kimi-K2",
                "max_length": 128000,
                # When using ReActAgentV2, streaming (i.e., setting "stream": True)
                # is not supported.
                # "stream": False,
            }
        ],
        project="Stock Agent",  # 项目名称
        name="StockAgent",  # 运行时名称
        disable_saving=True,  # 是否禁用文件保存，推荐开启
        save_dir="./runs",  # 保存目录
        save_log=True,  # 是否保存日志
        save_code=True,  # 是否保存此次运行的代码
        save_api_invoke=True,  # 保存 API 调用
        cache_dir="~/.cache",  # 缓存目录，用于缓存 Embedding 和其它
        use_monitor=True,  # 是否监控 token 使用情况
        logger_level="INFO",  # 日志级别
        # studio_url="http://localhost:3000"
    )

    # Add MCP servers
    toolkit = ServiceToolkit()
    toolkit.add_mcp_servers(
        {
            "mcpServers": {
                "stock_rag_tools": {
                    "type": "sse",
                    "url": "http://********:8000/sse",
                }
            },
        },
    )

    OpenAIFormatter.supported_model_regexes.append('Kimi-K2')
    OpenAIFormatter.supported_model_regexes.append('deepseek-r1')
    OpenAIFormatter.supported_model_regexes.append('deepseek-v3')

    now = datetime.now().strftime("%Y-%m-%d")

    # CEO
    with open('prompts/ceo.txt', 'r', encoding='utf-8') as f:
        ceo_prompt = f.read().format(now=now)
    agent_ceo = DialogAgent(
        name="Morgan",
        model_config_name="规划师",
        service_toolkit=toolkit,
        sys_prompt=ceo_prompt,
    )

    # 信息检索专家
    with open('prompts/leo.txt', 'r', encoding='utf-8') as f:
        leo_prompt = f.read().format(now=now)
    agent_leo = ReActAgentV2(
        name="Leo",
        max_iters=5,
        model_config_name="工具使用者",
        service_toolkit=toolkit,
        sys_prompt=leo_prompt,
    )

    # 题材挖掘专家
    with open('prompts/ken.txt', 'r', encoding='utf-8') as f:
        ken_prompt = f.read().format(now=now)
    agent_ken = DialogAgent(
        name="Ken",
        max_iters=3,
        model_config_name="思考者",
        service_toolkit=toolkit,
        sys_prompt=ken_prompt,
    )

    # 题材分析专家
    with open('prompts/lus.txt', 'r', encoding='utf-8') as f:
        lus_prompt = f.read().format(now=now)
    agent_lus = DialogAgent(
        name="Lus",
        max_iters=3,
        model_config_name="思考者",
        service_toolkit=toolkit,
        sys_prompt=lus_prompt,
    )

    # 秘书
    with open('prompts/jess.txt', 'r', encoding='utf-8') as f:
        jess_prompt = f.read().format(now=now)
    agent_jess = DialogAgent(
        name="Jess",
        max_iters=3,
        model_config_name="规划师",
        service_toolkit=toolkit,
        sys_prompt=jess_prompt
    )

    agent_names = {'Morgan': agent_ceo, 'Leo': agent_leo, 'Ken': agent_ken, 'Lus': agent_lus, 'Jess': agent_jess}

    return agent_names

def init_file_communication(session_id):
    """初始化文件通信"""
    global SESSION_ID, MESSAGE_FILE

    try:
        SESSION_ID = session_id

        # 创建消息目录
        message_dir = Path("messages")
        message_dir.mkdir(exist_ok=True)

        # 设置消息文件路径
        MESSAGE_FILE = message_dir / f"session_{SESSION_ID}.jsonl"

        # 清空或创建消息文件
        with open(MESSAGE_FILE, 'w', encoding='utf-8') as f:
            f.write("")  # 创建空文件

        print(f"文件通信初始化成功，会话ID: {SESSION_ID}")
        print(f"消息文件: {MESSAGE_FILE}")
        return True
    except Exception as e:
        print(f"文件通信初始化失败: {e}")
        SESSION_ID = None
        MESSAGE_FILE = None
        return False

def process_user_message(message, session_id):
    """处理用户消息"""
    try:
        # 初始化文件通信
        if not init_file_communication(session_id):
            print("文件通信初始化失败，使用标准输出模式")

        # 初始化代理
        agent_names = init_agent()
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 创建用户消息
        if '分析' not in message:
            new_message = f"请分析题材：{message}"
        else:
            new_message = message
        msg = Msg(
            name="user",
            content=f"{new_message}，现在是{now}",
            role="user",
        )

        # 处理消息
        memory = [msg]
        memory_map = {}
        response_msg = agent_names['Morgan'](msg)
        memory.append(response_msg)

        # 执行循环调用
        loop_call(response_msg, memory, memory_map, agent_names)

        # 发送完成信号
        if MESSAGE_FILE and SESSION_ID:
            completion_message = {
                'name': 'System',
                'content': '✅ **所有专家分析完成**',
                'role': 'system',
                'timestamp': datetime.now().strftime("%H:%M:%S"),
                'session_id': SESSION_ID,
                'type': 'completion'
            }
            write_message_to_file(completion_message)

        print("=== 代理处理完成 ===")

    except Exception as e:
        print(f"处理用户消息时出错: {e}")
        import traceback
        traceback.print_exc()

        # 发送错误信号
        if MESSAGE_FILE and SESSION_ID:
            error_message = {
                'name': 'System',
                'content': f'❌ 处理错误: {str(e)}',
                'role': 'system',
                'timestamp': datetime.now().strftime("%H:%M:%S"),
                'session_id': SESSION_ID,
                'type': 'error'
            }
            write_message_to_file(error_message)

def main():
    """主函数，支持命令行参数"""
    if len(sys.argv) >= 3:
        # 从命令行参数获取消息和会话ID
        message = sys.argv[1]
        session_id = sys.argv[2]
        print(f"处理消息: {message}, 会话ID: {session_id}")
        process_user_message(message, session_id)
    else:
        # 默认模式（兼容原有调用方式）
        print("使用默认模式")
        agent_names = init_agent()
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        msg = Msg(
            name="user",
            content=f"请分析题材：光伏，现在是{now}",
            role="user",
        )
        memory = [msg]
        memory_map = {}
        response_msg = agent_names['Morgan'](msg)
        memory.append(response_msg)
        loop_call(response_msg, memory, memory_map, agent_names)

if __name__ == "__main__":
    main()


