<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具折叠测试</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <h1>工具折叠样式测试</h1>
        
        <h2>1. 工具调用测试</h2>
        <div class="tool-collapsible tool-call-collapsible">
            <div class="tool-header" onclick="toggleToolContent('test-tool-1')">
                <span>
                    <span class="tool-icon">🔧</span>
                    工具调用: search_stock_news
                </span>
                <span class="tool-toggle" id="toggle-test-tool-1">▼</span>
            </div>
            <div class="tool-content" id="content-test-tool-1">
参数: {"query": "人工智能 题材 逻辑 分支 潜力 2025"}
            </div>
        </div>
        
        <h2>2. 工具结果测试</h2>
        <div class="tool-collapsible tool-result-collapsible">
            <div class="tool-header" onclick="toggleToolContent('test-result-1')">
                <span>
                    <span class="tool-icon">✅</span>
                    工具执行完成: search_stock_news
                </span>
                <span class="tool-toggle" id="toggle-test-result-1">▼</span>
            </div>
            <div class="tool-content" id="content-test-result-1">
获取数据: 5 条记录
数据预览: 人工智能概念股午后全面爆发...
            </div>
        </div>
        
        <h2>3. Call mention测试</h2>
        <p>这是一个 <span class="call-mention">@Ken</span> 的测试</p>
        
        <h2>4. 混合测试</h2>
        <div class="message assistant">
            <div class="message-header">
                <span class="message-name">Leo</span>
                <span class="message-time">14:30:00</span>
            </div>
            <div class="message-content">
                <p>收到任务，我将立即检索人工智能题材的最新信息。</p>
                
                <div class="tool-collapsible tool-call-collapsible">
                    <div class="tool-header" onclick="toggleToolContent('test-mixed-1')">
                        <span>
                            <span class="tool-icon">🔧</span>
                            工具调用: search_stock_news
                        </span>
                        <span class="tool-toggle" id="toggle-test-mixed-1">▼</span>
                    </div>
                    <div class="tool-content" id="content-test-mixed-1">
参数: {"query": "人工智能 题材 逻辑 分支 潜力 2025"}
                    </div>
                </div>
                
                <p>正在处理中...</p>
            </div>
        </div>
    </div>
    
    <script>
        // 切换工具内容显示
        function toggleToolContent(id) {
            console.log('🔄 切换工具内容:', id);
            const content = document.getElementById(`content-${id}`);
            const toggle = document.getElementById(`toggle-${id}`);
            
            if (content && toggle) {
                const isExpanded = content.classList.contains('expanded');
                console.log('当前状态:', isExpanded ? '展开' : '折叠');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    toggle.classList.remove('expanded');
                    console.log('✅ 已折叠');
                } else {
                    content.classList.add('expanded');
                    toggle.classList.add('expanded');
                    console.log('✅ 已展开');
                }
            } else {
                console.log('❌ 未找到元素:', { content: !!content, toggle: !!toggle });
            }
        }
        
        // 确保函数在全局作用域
        window.toggleToolContent = toggleToolContent;
        
        console.log('✅ 工具折叠测试页面加载完成');
    </script>
</body>
</html>
