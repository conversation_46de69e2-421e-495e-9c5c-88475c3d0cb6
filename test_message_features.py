#!/usr/bin/env python3
"""
测试消息功能：连续消息合并和工具折叠
"""

import os
import sys
import json
import uuid
import time
from pathlib import Path
from datetime import datetime

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.system('chcp 65001 >nul 2>&1')
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

def create_test_messages():
    """创建测试消息"""
    
    # 创建测试会话ID
    session_id = str(uuid.uuid4())
    message_file = Path("messages") / f"session_{session_id}.jsonl"
    
    print(f"创建测试消息文件: {message_file}")
    
    # 创建消息目录
    message_file.parent.mkdir(exist_ok=True)
    
    # 创建空文件
    with open(message_file, 'w', encoding='utf-8') as f:
        f.write("")
    
    # 测试消息
    test_messages = [
        # 1. Morgan的第一条消息
        {
            'name': '<PERSON>',
            'content': '[Call @Ken]: 请分析人工智能题材的投资机会',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_1'
        },
        
        # 2. Morgan的连续消息（应该合并）
        {
            'name': 'Morgan',
            'content': '重点关注以下几个方向：\n1. AI芯片\n2. 数据服务\n3. 应用端',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_2'
        },
        
        # 3. Morgan的第三条连续消息（应该合并）
        {
            'name': 'Morgan',
            'content': '请尽快完成分析，市场变化很快。',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_3'
        },
        
        # 4. Ken的回复（新气泡）
        {
            'name': 'Ken',
            'content': '收到！我将立即开始分析。',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_4'
        },
        
        # 5. Ken的工具调用消息
        {
            'name': 'Ken',
            'content': '🔧 **工具调用**: search_stock_news\n📋 **参数**: `{"query": "人工智能 AI芯片 投资机会", "limit": 10}`',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_5'
        },
        
        # 6. 工具结果（system消息，应该合并）
        {
            'name': 'system',
            'content': '✅ **工具执行完成**: search_stock_news\n📊 **获取数据**: 10 条记录\n📄 **数据预览**: 找到多条关于AI芯片的最新新闻...',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_6',
            'raw_content': [
                {
                    'type': 'tool_result',
                    'tool_use_id': 'test_tool_1',
                    'content': [
                        {
                            'type': 'text',
                            'text': '✅ **工具执行完成**: search_stock_news\n📊 **获取数据**: 10 条记录\n📄 **数据预览**: 找到多条关于AI芯片的最新新闻...'
                        }
                    ]
                }
            ]
        },
        
        # 7. Ken的分析结果（连续消息，应该合并）
        {
            'name': 'Ken',
            'content': '基于检索结果，我发现以下投资机会：',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_7'
        },
        
        # 8. Ken的详细分析（连续消息，应该合并）
        {
            'name': 'Ken',
            'content': '## AI芯片板块\n\n**寒武纪（688256）**\n- 国产AI芯片龙头\n- 技术实力强劲\n- 政策支持力度大\n\n**海光信息（688041）**\n- CPU+GPU双轮驱动\n- 服务器市场前景广阔',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_8'
        },
        
        # 9. Leo的回复（新气泡）
        {
            'name': 'Leo',
            'content': '[Call @Ken]: 分析很全面！我补充一些数据支撑。',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_9'
        },
        
        # 10. Leo的工具调用
        {
            'name': 'Leo',
            'content': '🔧 **工具调用**: get_stock_knowledge_data\n📋 **参数**: `{"keywords": ["寒武纪", "海光信息", "AI芯片"], "data_type": "financial"}`',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_10'
        },
        
        # 11. 复杂工具结果
        {
            'name': 'system',
            'content': '✅ **工具执行完成**: get_stock_knowledge_data\n📊 **获取数据**: 25 条记录\n📄 **数据预览**: 包含财务数据、技术指标、行业对比等详细信息',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_11',
            'raw_content': [
                {
                    'type': 'tool_result',
                    'tool_use_id': 'test_tool_2',
                    'content': [
                        {
                            'type': 'text',
                            'text': '✅ **工具执行完成**: get_stock_knowledge_data\n📊 **获取数据**: 25 条记录\n📄 **数据预览**: 包含财务数据、技术指标、行业对比等详细信息'
                        }
                    ]
                }
            ]
        },
        
        # 12. 完成消息
        {
            'name': 'System',
            'content': '✅ 测试消息创建完成！\n\n预期效果：\n- Morgan的3条消息应该在一个气泡中\n- Ken的分析应该在一个气泡中\n- 工具调用和结果应该显示为折叠样式',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'type': 'completion'
        }
    ]
    
    # 逐个写入消息
    for i, msg in enumerate(test_messages):
        print(f"写入测试消息 {i+1}: {msg['name']}")
        
        with open(message_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(msg, ensure_ascii=False) + '\n')
            f.flush()
        
        time.sleep(0.3)
    
    print(f"\n测试完成，消息文件: {message_file}")
    print(f"会话ID: {session_id}")
    
    return session_id

def update_debug_session_id(session_id):
    """更新app_file.py中的DEBUG_SESSION_ID"""
    
    app_file = "app_file.py"
    
    if not os.path.exists(app_file):
        print(f"❌ 文件不存在: {app_file}")
        return False
    
    # 读取文件内容
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换DEBUG_SESSION_ID
    import re
    new_content = re.sub(
        r'DEBUG_SESSION_ID = "[^"]*"',
        f'DEBUG_SESSION_ID = "{session_id}"',
        content
    )
    
    # 写回文件
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ 已更新 DEBUG_SESSION_ID = \"{session_id}\"")
    return True

if __name__ == "__main__":
    print("🧪 创建消息功能测试数据")
    print("=" * 50)
    
    session_id = create_test_messages()
    
    if update_debug_session_id(session_id):
        print("\n🚀 测试准备完成！")
        print("\n启动步骤：")
        print("1. 确保 DEBUG_MODE = True")
        print("2. 运行: python app_file.py")
        print("3. 打开浏览器访问: http://localhost:5000")
        print("4. 点击'初始化系统'")
        print("5. 输入任意消息开始测试")
        print("\n📝 预期效果：")
        print("- 同一人的连续消息会合并到一个气泡中")
        print("- 工具调用和结果会显示为可折叠的样式")
        print("- [Call @xxx] 会显示为蓝色的 @xxx")
    else:
        print("❌ 更新配置失败")
