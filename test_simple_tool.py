#!/usr/bin/env python3
"""
创建简单的工具调用测试消息
"""

import os
import sys
import json
import uuid
import time
from pathlib import Path
from datetime import datetime

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.system('chcp 65001 >nul 2>&1')
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

def create_simple_tool_test():
    """创建简单的工具调用测试"""
    
    # 创建测试会话ID
    session_id = str(uuid.uuid4())
    message_file = Path("messages") / f"session_{session_id}.jsonl"
    
    print(f"创建简单工具测试消息文件: {message_file}")
    
    # 创建消息目录
    message_file.parent.mkdir(exist_ok=True)
    
    # 创建空文件
    with open(message_file, 'w', encoding='utf-8') as f:
        f.write("")
    
    # 测试消息 - 直接在content中包含工具调用格式
    test_messages = [
        # 1. 直接包含工具调用格式的消息
        {
            'name': 'Ken',
            'content': '我需要检索一些数据：\n\n🔧 **工具调用**: search_stock_news\n📋 **参数**: `{"query": "人工智能", "limit": 5}`\n\n正在处理中...',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_1'
        },
        
        # 2. 工具结果消息
        {
            'name': 'Ken',
            'content': '数据检索完成：\n\n✅ **工具执行完成**: search_stock_news\n📊 **获取数据**: 5 条记录\n📄 **数据预览**: 人工智能概念股午后全面爆发...\n\n基于这些数据分析...',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_2'
        },
        
        # 3. 复杂参数的工具调用
        {
            'name': 'Leo',
            'content': '🔧 **工具调用**: get_stock_knowledge_data\n📋 **参数**: `{"keywords": ["AI芯片", "寒武纪"], "data_type": "financial", "time_range": "2024-01-01 to 2024-12-31"}`',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_3'
        },
        
        # 4. 复杂工具结果
        {
            'name': 'Leo',
            'content': '✅ **工具执行完成**: get_stock_knowledge_data\n📊 **获取数据**: 25 条记录\n📄 **数据预览**: 包含财务数据、技术指标、行业对比等详细信息',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_4'
        },
        
        # 5. 简单工具结果
        {
            'name': 'System',
            'content': '✅ **工具执行完成**: update_database\n📊 **返回结果**: 数据库更新成功',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_5'
        },
        
        # 6. 最简单的工具结果
        {
            'name': 'System',
            'content': '✅ **工具执行完成**: cleanup_cache',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_6'
        },
        
        # 7. 包含Call mention的消息
        {
            'name': 'Morgan',
            'content': '[Call @Ken]: 请基于上述数据进行深度分析\n\n重点关注：\n1. 技术趋势\n2. 投资机会\n3. 风险点',
            'role': 'assistant',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'message_id': 'test_7'
        },
        
        # 8. 完成消息
        {
            'name': 'System',
            'content': '✅ 简单工具调用测试完成！\n\n预期效果：\n- 工具调用应该显示为蓝色可折叠块\n- 工具结果应该显示为绿色可折叠块\n- [Call @Ken] 应该显示为蓝色的 @Ken\n- 点击工具块可以展开/折叠',
            'role': 'system',
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'session_id': session_id,
            'type': 'completion'
        }
    ]
    
    # 逐个写入消息
    for i, msg in enumerate(test_messages):
        print(f"写入简单测试消息 {i+1}: {msg['name']}")
        print(f"  内容预览: {msg['content'][:50]}...")
        
        with open(message_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(msg, ensure_ascii=False) + '\n')
            f.flush()
        
        time.sleep(0.3)
    
    print(f"\n简单工具测试完成，消息文件: {message_file}")
    print(f"会话ID: {session_id}")
    
    return session_id

def update_debug_session_id(session_id):
    """更新app_file.py中的DEBUG_SESSION_ID"""
    
    app_file = "app_file.py"
    
    if not os.path.exists(app_file):
        print(f"❌ 文件不存在: {app_file}")
        return False
    
    # 读取文件内容
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换DEBUG_SESSION_ID
    import re
    new_content = re.sub(
        r'DEBUG_SESSION_ID = "[^"]*"',
        f'DEBUG_SESSION_ID = "{session_id}"',
        content
    )
    
    # 写回文件
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ 已更新 DEBUG_SESSION_ID = \"{session_id}\"")
    return True

if __name__ == "__main__":
    print("🔧 创建简单工具调用测试数据")
    print("=" * 50)
    
    session_id = create_simple_tool_test()
    
    if update_debug_session_id(session_id):
        print("\n🚀 简单工具测试准备完成！")
        print("\n启动步骤：")
        print("1. 确保 DEBUG_MODE = True")
        print("2. 运行: python app_file.py")
        print("3. 打开浏览器访问: http://localhost:5000")
        print("4. 打开开发者工具查看Console输出")
        print("5. 点击'初始化系统'")
        print("6. 输入任意消息开始测试")
        print("\n📝 预期效果：")
        print("- 工具调用显示为蓝色可折叠块")
        print("- 工具结果显示为绿色可折叠块")
        print("- [Call @Ken] 显示为蓝色的 @Ken")
        print("- 点击工具块标题可以展开/折叠")
        print("- Console中显示详细的处理日志")
        print("\n🔍 调试重点：")
        print("- 查看 '🔧 开始处理工具调用' 日志")
        print("- 查看正则匹配结果")
        print("- 查看生成的折叠组件HTML")
    else:
        print("❌ 更新配置失败")
